import request from '@/utils/request'

export function getUserDataPermissions(userId) {
  return request({
    url: '/systemUserDataPermission/getUserDataPermissions',
    method: 'get',
    params: {
      userId,
    },
  })
}

export function saveUserDataPermissions(data) {
  return request({
    url: '/systemUserDataPermission/saveUserDataPermissions',
    method: 'post',
    data,
  })
}

export function getTaskConfigOptions() {
  return request({
    url: '/systemUserDataPermission/getTaskConfigOptions',
    method: 'get',
  })
}

export function getCounterOrderConfigOptions() {
  return request({
    url: '/systemUserDataPermission/getCounterOrderConfigOptions',
    method: 'get',
  })
}
