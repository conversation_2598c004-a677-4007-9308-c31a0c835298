import qs from 'qs'
import settings from '@/settings'

/**
 * 此mixin用于记录查询参数到url，作用是刷新页面时，查询条件以及分页参数依然保留
 * 适用于常规的分页查询界面，如有别的需求，需进行扩展适配
 * 用法：
 * 1、在页面组件中引入此mixin
 * import { queryParamsMixin } from '@/mixin'
 * 2、在组件的属性中引用queryParamsMixin
 * mixins: [queryParamsMixin],
 * 3、在data属性中，声明urlQuery = true
 * 4、在x-table组件的getList方法中调用saveQueryParams记录查询参数
 * 5、在清空查询条件时，调用removeQueryParams删除url里的查询参数
 * 6、如在页面创建时，需要对查询参数做另外的处理，需定义handleQueryParams函数，并返回处理过后的queryParams
 *
 * 具体如何使用请参考views/share-sdk/index.vue页面
 *
 * 注意：默认的表格ref为table
 * 默认的分页page=1，limit=50
 *
 */
const mixin = {
  created() {
    if (!settings.urlQuery || !this.urlQuery) {
      return
    }
    // 判断url是否有参数
    let query = this.$router.history.current.query
    // console.log('created params: ', query)
    let queryParams = query.p || {}
    if (this.handleQueryParams) {
      // 有一些参数需要进行处理，比如：下拉框类型不一致将不会选中，所以就需要将字符串转成数字，就可以通过handleQueryParams函数进行处理
      queryParams = this.handleQueryParams(queryParams)
    }
    // 分页处理
    const { page, limit } = queryParams
    // console.log('page:', page, 'limit:', limit)
    if (page && limit && (page != 1 || limit != 50)) {
      // 如果分页不是默认的page=1,limit=50，则更新分页的ui
      this.$nextTick(() => {
        this.$refs.table.updatePageUI(parseInt(page), parseInt(limit))
      })
    }
    this.queryParams = Object.assign({}, this.queryParams, queryParams)
  },
  methods: {
    saveQueryParams(queryParams) {
      // console.log('saveQueryParams----')
      // 判断是否支持记录查询参数
      if (!settings.urlQuery || !this.urlQuery) {
        // this.$refs.table.refresh(true)
        return
      }
      // 把参数通过qs模块转换成url的的形式，然后与当前url进行字符串比较是否相等，来判断是否需要跳转和更新url
      queryParams = queryParams || this.queryParams
      let qp = Object.assign({}, queryParams)
      if (qp.page == 1 && qp.limit == 50) {
        // 分页约定每一页50
        delete qp.page
        delete qp.limit
      }
      const queryParamsStr = qs.stringify({ p: qp })
      let currentParamsStr = ''
      if (location.search) {
        currentParamsStr = window.location.search.substring(1)
        currentParamsStr = currentParamsStr
      }
      // console.log('queryparams:', this.queryParams)
      // console.log('queryParamsStr:', queryParamsStr)
      // console.log('currentParamsStr:', currentParamsStr)
      // 判断url是否相等，如果相等，不跳转
      if (queryParamsStr != currentParamsStr) {
        // 更新url
        this.pushWithQueryParams(qp)
      }

      // this.$refs.table.refresh(true)
    },
    removeQueryParams() {
      if (!location.search) {
        return
      }
      this.queryParams = {}
      this.$refs.table.updatePageUI(1, 50)
      if (!settings.urlQuery || !this.urlQuery) {
        if (location.search) {
          this.pushWithQueryParams({})
        }
        return
      }
      // 去掉url所有参数
      this.pushWithQueryParams({})
    },

    pushWithQueryParams(queryParams) {
      // console.log(queryParams)
      this.$router.replace({}).catch(() => {})
      this.$router
        .replace({
          query: {
            p: queryParams,
          },
        })
        .catch(() => {})
    },
  },
}

export default mixin
