import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/deliverArea/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/deliverArea/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addDeliverArea(data) {
  return request({
    url: '/deliverArea/add',
    method: 'post',
    data: data,
  })
}

export function editDeliverArea(data) {
  return request({
    url: '/deliverArea/edit',
    method: 'post',
    data: data,
  })
}

export function delDeliverArea(id) {
  return request({
    url: '/deliverArea/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function getAreaOptions() {
  return request({
    url: '/deliverArea/options',
    method: 'get',
  })
}

//----------DeliverArea结束----------
