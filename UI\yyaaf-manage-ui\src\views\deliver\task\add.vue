<template>
  <div class="app-container">
    <el-card>
      <!-- 步骤条 -->
      <el-steps :active="activeStep" finish-status="success">
        <el-step title="任务信息" />
        <el-step title="渠道分配" />
        <el-step title="完成" />
      </el-steps>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="110px"
        style="margin-top: 20px"
      >
        <el-row>
          <!-- 第一步：任务信息 -->
          <div v-if="activeStep === 0">
            <div class="form-section">
              <h3 class="section-title">任务信息</h3>
              <el-col :span="24">
                <el-form-item label="任务Id" prop="id">
                  <el-input v-model="form.id" disabled="false" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="任务名称" prop="name">
                  <el-input v-model="form.name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="任务容量" prop="target">
                  <el-input-number v-model="form.target" placeholder="" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单主号容量" prop="singleAccountTarget">
                  <el-input-number v-model="form.singleAccountTarget" placeholder="" />
                </el-form-item>
              </el-col>
            </div>

            <div class="form-section">
              <h3 class="section-title">地区配置</h3>
              <el-col :span="24">
                <el-form-item label="投放地区" prop="areaId">
                  <x-select
                    v-model="form.areaId"
                    url="/deliverArea/options"
                    style="width: 100%"
                  ></x-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="时区" prop="utc">
                  <x-select
                    v-model="form.utc"
                    :options="utcTimeZoneOptions"
                    style="width: 100%"
                  ></x-select>
                </el-form-item>
              </el-col>
            </div>

            <div class="form-section">
              <h3 class="section-title">其他配置</h3>
              <el-col :span="24">
                <el-form-item label="粉丝单价" prop="price">
                  <el-input-number v-model="form.price" placeholder="" style="width: 30%" />￥
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否启用" prop="enable">
                  <x-radio v-model="form.enable" button :options="enableOptions" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="每日结单时间" prop="dailyStatementTime">
                  <el-time-picker
                    v-model="form.dailyStatementTime"
                    :picker-options="{
                      selectableRange: '00:30:00 - 23:30:00',
                    }"
                    value-format="HH:mm:ss"
                    placeholder="选择时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <!--<el-col :span="12">
                <el-form-item label="是否修改" prop="state">
                  <x-radio v-model="form.state" button :options="statusOptions" />
                </el-form-item>
              </el-col>-->
            </div>

            <div class="form-section">
              <h3 class="section-title">计数器配置</h3>
              <el-col :span="24">
                <el-form-item label="计数器" prop="counterOrderId">
                  <x-select
                    :filterable="true"
                    v-model="form.counterOrderId"
                    url="/counterOrderConfig/options"
                    style="width: 100%"
                    @change="handleCounterOrderChange"
                  ></x-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="计数器Url" prop="counterOrderUrl">
                  <el-input v-model="form.counterOrderUrl" placeholder="请输入计数器Url" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计数器账号" prop="counterOrderAccount">
                  <el-input v-model="form.counterOrderAccount" placeholder="请输入计数器密码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计数器密码" prop="counterOrderPassWord">
                  <el-input v-model="form.counterOrderPassWord" placeholder="请输入计数器密码" />
                </el-form-item>
              </el-col>
            </div>

            <div class="form-section">
              <h3 class="section-title">渠道和主号配置</h3>
              <el-col :span="12">
                <el-form-item label="渠道" prop="channelIds">
                  <el-autocomplete
                    class="inline-input"
                    :fetch-suggestions="querySearch"
                    placeholder="请输入需要查询的渠道标识"
                    :trigger-on-focus="false"
                    v-model="queryCid"
                    style="width: 100%; margin-bottom: 10px"
                    :debounce="50"
                    @select="handleSelect"
                    prefix-icon="el-icon-search"
                  ></el-autocomplete>
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 15 }"
                    v-model="form.channelIds"
                    placeholder="请输入渠道标识,多个换行分隔"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <div
                  style="
                    margin-left: 110px;
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                  "
                >
                  <el-button type="primary" @click="getOrderAccounts" size="mini"
                    >获取当日全部主号</el-button
                  >
                  <span style="font-size: 12px; color: #666; margin-left: 10px">
                    获取当日所有已抓取的主号账户，每日<b style="color: green">任务投放中</b
                    >更新主号时候使用这个，不会导致下线主号数据丢失
                  </span>
                </div>
                <div style="margin-left: 110px; display: flex; align-items: center">
                  <el-button type="warning" @click="getOnlineAccounts" size="mini"
                    >获取当日在线主号</el-button
                  >
                  <span style="font-size: 12px; color: #666; margin-left: 10px">
                    获取当日在线状态的主号账户，每日<b style="color: red">任务投放结束</b
                    >分配工单的时候使用这个，只获取最新的主号分配，<b style="color: red"
                      >会删除下线账号数据</b
                    >
                  </span>
                </div>
                <el-form-item style="margin-top: 30px" label="主号" prop="accountIds">
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 15 }"
                    v-model="form.accountIds"
                    placeholder="请输入主号,多个换行分隔"
                  />
                </el-form-item>
              </el-col>
            </div>
          </div>
        </el-row>

        <!-- 第二步：渠道分配 -->
        <div v-if="activeStep === 1">
          <el-row>
            <el-col :span="12">
              <el-form-item label="渠道绑定">
                <el-select
                  v-model="selectedChannel"
                  placeholder="选择渠道"
                  @change="updateAvailableAccounts"
                  style="width: 100%"
                >
                  <el-option
                    v-for="channel in availableChannels"
                    :key="channel"
                    :label="channel"
                    :value="channel"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="账号绑定">
                <el-select
                  v-model="selectedAccounts"
                  multiple
                  placeholder="选择主号"
                  style="width: 100%"
                >
                  <el-option
                    v-for="account in availableAccounts"
                    :key="account"
                    :label="account"
                    :value="account"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-button
              type="primary"
              style="margin-top: 10px; margin-left: 30px"
              @click="bindAccounts"
              >绑定</el-button
            >

            <el-col :span="24">
              <!-- 显示绑定关系 -->
              <el-table :data="bindings" style="margin-top: 20px; margin-bottom: 20px" border>
                <el-table-column
                  prop="channel"
                  label="渠道"
                  align="center"
                  min-width="200"
                ></el-table-column>
                <el-table-column prop="accounts" label="绑定主号" align="center" min-width="500">
                  <template #default="{ row }">
                    {{ row.accounts.join(', ') }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" min-width="100">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="bind(row.channel)"
                      >添加</el-button
                    >
                    <el-button type="danger" size="small" @click="unbind(row.channel)"
                      >解绑</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>

        <!-- 第三步：确认提交 -->
        <div v-if="activeStep === 2">
          <el-alert title="请确认您的信息" type="info" show-icon />
          <p><strong>任务名称：</strong>{{ form.name }}</p>
          <p><strong>目标：</strong>{{ form.target }}</p>
          <!--<p><strong>时区：</strong>{{ form.utc }}</p>-->
          <p><strong>计数器：</strong>{{ form.counterOrderUrl }}</p>
          <p><strong>绑定关系：</strong></p>
          <ul>
            <li v-for="binding in bindings" :key="binding.channel">
              <strong>{{ binding.channel }}：</strong> {{ binding.accounts.join(', ') }}
            </li>
          </ul>
        </div>

        <!-- 按钮区域 -->
        <el-form-item>
          <el-button v-if="activeStep > 0" @click="prevStep">上一步</el-button>
          <el-button v-if="activeStep < 2" type="primary" @click="nextStep">下一步</el-button>
          <el-button v-if="activeStep === 2" type="success" @click="submitForm">提交</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { enableOptions, utcTimeZoneOptions, getChannelOptions } from '@/utils/options'
import { deliverTaskApi, counterOrderConfigApi, counterOrderDateRecordsApi } from '@/api'
export default {
  data() {
    return {
      // 1：编辑，9：复制
      operation: 0,
      activeStep: 0,
      form: {
        enable: true,
        state: 0,
        name: '',
        utc: 8,
        channelIds: '',
        accountIds: '',
        price: 12,
        singleAccountTarget: 20,
      },
      rules: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        utc: [{ required: true, message: '请输入时区', trigger: 'blur' }],
        target: [{ required: true, message: '请输入容量', trigger: 'blur' }],
        channelIds: [{ required: true, message: '请输入投放渠道标识', trigger: 'blur' }],
        accountIds: [{ required: true, message: '请输入投放主号', trigger: 'blur' }],
        price: [{ required: true, message: '请输入粉丝单价', trigger: 'blur' }],
        dailyStatementTime: [{ required: true, message: '请选择每日结单时间', trigger: 'blur' }],
      },
      selectedChannel: '',
      selectedAccounts: [],
      bindings: [],
      utcTimeZoneOptions,
      enableOptions,
      channelOptions: [],
      queryCid: '',
      statusOptions: [
        { label: '禁止渠道用户修改', value: 0 },
        { label: '允许渠道用户修改', value: 1 },
      ],
    }
  },
  async created() {
    let query = this.$router.history.current.query
    if (query.operation) this.operation = query.operation
    if (query.id) await this.getDetail(query.id)
    this.channelOptions = await getChannelOptions()
  },
  computed: {
    channelList() {
      return this.form.channelIds.split('\n').filter((c) => c.trim())
    },
    accountList() {
      return this.form.accountIds.split('\n').filter((a) => /^[0-9]+$/.test(a.trim()))
    },
    availableAccounts() {
      const usedAccounts = new Set(this.bindings.flatMap((b) => b.accounts || [])) // 避免 undefined
      return [...new Set(this.accountList)].filter((a) => !usedAccounts.has(a)) // 确保 accountList 也去重
    },
    availableChannels() {
      const usedChannels = new Set(this.bindings.flatMap((b) => b.channel || [])) // 避免 undefined
      return [...new Set(this.channelList)].filter((c) => !usedChannels.has(c)) // 确保 channelList 也去重
    },
    accountIds() {
      return this.accountList.join('\n')
    },
  },
  watch: {
    accountList() {
      this.cleanBindings()
    },
  },
  methods: {
    async getDetail(id) {
      this.$xloading.show()
      const res = await deliverTaskApi.getDetail(id)
      if (res.code == 0) {
        this.form = res.data
        this.bindings = res.data.bindings
        if (this.operation == 9) this.form.id = null
      }
      this.$xloading.hide()
    },
    // 清理绑定关系
    cleanBindings() {
      const validAccounts = new Set(this.accountList) // 获取 form.accountIds 里的账号列表

      this.bindings = this.bindings
        .map((binding) => {
          return {
            ...binding,
            accounts: binding.accounts.filter((account) => validAccounts.has(account)), // 只保留存在的账号
          }
        })
        .filter((binding) => binding.accounts.length > 0) // 删除没有绑定账号的渠道
    },
    updateAvailableAccounts() {
      this.selectedAccounts = []
    },
    bindAccounts() {
      if (!this.selectedChannel || this.selectedAccounts.length === 0) return
      this.bindings.push({ channel: this.selectedChannel, accounts: [...this.selectedAccounts] })
      this.selectedChannel = ''
      this.selectedAccounts = []
    },
    bind(channel) {
      let channelBind = this.bindings.find((b) => b.channel === channel)
      if (channelBind) {
        channelBind.accounts = [...channelBind.accounts, ...this.selectedAccounts]
      } else {
        // 如果 channel 还没有绑定，则创建一个新的绑定
        this.bindings.push({ channel, accounts: [...this.selectedAccounts] })
      }
      this.selectedAccounts = []
    },
    unbind(channel) {
      this.bindings = this.bindings.filter((b) => b.channel !== channel)
    },
    prevStep() {
      if (this.activeStep > 0) this.activeStep--
    },
    nextStep() {
      this.$refs.formRef.validate((valid) => {
        if (valid) this.activeStep++
      })
    },
    close() {
      this.$router.push({ path: '/deliver/delivertask' })
    },
    async submitForm() {
      /*if (this.availableAccounts.length > 0) {
        this.$xMsgError('存在没有被分配的主号，请返回操作！')
        return
      }*/
      this.form.bindings = this.bindings
      this.form.accountIds = this.accountIds
      const data = this.form
      console.log('提交数据', data)
      if (this.operation == 1) {
        const res = await deliverTaskApi.editDeliverTask(data)
        this.$xloading.hide()
        if (res.code == 0) {
          this.$xMsgSuccess('修改成功')
          this.close()
        } else {
          this.$xMsgError('操作失败！' + res.msg)
        }
      } else {
        const res = await deliverTaskApi.addDeliverTask(data)
        this.$xloading.hide()
        if (res.code == 0) {
          this.$xMsgSuccess('添加成功')
          this.close()
        } else {
          this.$xMsgError('操作失败！' + res.msg)
        }
      }
    },
    querySearch(queryString, cb) {
      //console.log(queryString)
      let restaurants = this.channelOptions
      let results = queryString
        ? restaurants.filter(
            (e) =>
              e.value.toLowerCase().indexOf(queryString.toLowerCase()) > -1 ||
              e.label.toLowerCase().indexOf(queryString.toLowerCase()) > -1
          )
        : restaurants

      results = results.map((o) => {
        return {
          value: o.label + ' 【' + o.value + '】',
          name: o.value,
        }
      })

      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    handleSelect(item) {
      let idsArray = this.form.channelIds.split('\n').filter((c) => c.trim()) // 拆分为数组并去除空行
      if (!idsArray.includes(item.name)) {
        // 检查是否已存在
        idsArray.push(item.name) // 只有不存在时才添加
        this.form.channelIds = idsArray.join('\n') // 重新拼接回字符串
      }
      this.queryCid = '' // 清空搜索框
    },
    async handleCounterOrderChange() {
      const counterOrderId = this.form.counterOrderId
      //console.log('counterOrderId', counterOrderId)
      const res = await counterOrderConfigApi.getDetail(counterOrderId)
      if (res.code == 0) {
        this.$set(this.form, 'counterOrderUrl', res.data.url || '')
        this.$set(this.form, 'counterOrderAccount', res.data.account || '')
        this.$set(this.form, 'counterOrderPassWord', res.data.passWord || '')
        //this.form.counterOrderUrl = res.data.url
        //this.form.counterOrderPassWord = res.data.passWord
      }
    },

    async getOrderAccounts() {
      const counterOrderId = this.form.counterOrderId
      if (!counterOrderId || counterOrderId == -1) return this.$xMsgError('请先选择计数器！')
      const params = {
        CounterOrderConfigId: counterOrderId,
      }
      const res = await counterOrderDateRecordsApi.getAccounts(params)
      if (res.code == 0) {
        this.form.accountIds = res.data.join('\n')
      }
    },

    async getOnlineAccounts() {
      const counterOrderId = this.form.counterOrderId
      if (!counterOrderId || counterOrderId == -1) return this.$xMsgError('请先选择计数器！')
      const params = {
        CounterOrderConfigId: counterOrderId,
      }
      const res = await counterOrderDateRecordsApi.getOnlineAccounts(params)
      if (res.code == 0) {
        this.form.accountIds = res.data.join('\n')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.form-section {
  position: relative;
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
  overflow: hidden;

  &::after {
    content: '';
    display: table;
    clear: both;
  }

  .el-col {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  color: #606266;
  margin: -16px -16px 16px -16px;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  background: #fff;
  position: relative;
  padding-left: 24px;

  &::before {
    content: '';
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 14px;
    background: #409eff;
    border-radius: 1px;
  }
}
</style>
