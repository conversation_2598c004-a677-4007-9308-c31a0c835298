

<template>
    <div class="app-container">
      <el-card>
        <el-row ref="toolbar" class="table-toolbar">
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd" v-permission="['deliverArea:add']">新增</el-button>
        </el-row>
        
        <!-- 表格 -->
        <x-table
          ref="table"
          v-loading="loading"
          :data="tableData"
          row-key="id"
          :loadData="getList"
          :height="tableHeight"
          >
          <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
          <el-table-column prop="name" label="地区" align="center" min-width="250"></el-table-column>
          <el-table-column prop="timeZone" label="时区" align="center" min-width="100"></el-table-column>
          <el-table-column prop="sort" label="排序" align="center" min-width="100"></el-table-column>
   
          <el-table-column label="操作" min-width="160" align="center">
            <template slot-scope="scope">
              <el-button size="mini" 
                type="warning"
                @click="handleEdit(scope.row)"
                v-permission="['deliverArea:edit']"
              >修改</el-button>
              <el-button size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
                v-permission="['deliverArea:delete']"
              >删除</el-button>
            </template>
          </el-table-column>
        </x-table>
      </el-card>
  
      <edit-dialog ref="editDialog" @ok="handleOk"/>
    </div>
  </template>
  
  <script>
  import { tableHeightMixin } from '@/mixin'
  import { deliverAreaApi } from '@/api'
  import EditDialog from './edit'
  export default {
    components: {
      EditDialog
    },
    mixins:[tableHeightMixin],
    data () {
      return {
        queryParams: {},
        loading: false,
        tableData: {},
      }
    },
    methods: {
      queryReset () {
        this.queryParams = {}
        this.$refs.table.refresh(true)
      },
      async getList(params) {
        this.$xloading.show()
        params = Object.assign({}, params, this.queryParams)
        const res = await deliverAreaApi.getList(params)
        this.tableData = res
        this.$xloading.hide()
      },
      handleAdd () {
        this.$refs.editDialog.add()
      },
      handleEdit (row) {
        this.$refs.editDialog.edit(row)
      },
      async handleDelete (row) {
        this.$confirm('是否确认删除该数据项？', '提示', {
          type: 'warning'
        }).then(async () => {
          this.$xloading.show()
          const res = await deliverAreaApi.delDeliverArea(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        }).catch(() => {
          this.$xloading.hide()
        })
        
      },
  
      handleOk () {
        this.$refs.table.refresh()
      },
    }
  }
  </script>
  