<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24">
        <el-card>
          <el-tabs class="" tab-position="left">
            <el-tab-pane>
              <div class="tab-title" slot="label">修改密码</div>
              <password></password>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Password from './password'
export default {
  components: {
    Password,
  },
}
</script>

<style lang="scss" scoped>
.tab-title {
  width: 200px;
}
</style>
