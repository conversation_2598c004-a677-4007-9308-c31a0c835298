<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="地区">
          <x-select
            show-default
            v-model="queryParams.areaId"
            url="/deliverArea/options"
            @change="fetchTaskConfigOption"
          ></x-select>
        </el-form-item>
        <el-form-item label="任务">
          <x-select
            show-default
            v-model="queryParams.taskConfigId"
            :options="taskConfigOptions"
          ></x-select>
        </el-form-item>
        <el-form-item label="投放时段">
          <el-date-picker
            v-model="queryParams.date"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar"> </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :show-summary="true"
        :header-cell-style="{ fontSize: '14px' }"
        :cell-style="{ fontSize: '14px' }"
        :row-class-name="tableRowClassName"
        border
      >
        <el-table-column
          v-for="(col, index) in columns"
          :key="index"
          :prop="col.prop"
          :label="col.label"
          align="center"
          :min-width="col.width"
        >
          <template #default="scope">
            <task-info
              v-if="col.prop === 'taskName'"
              :taskId="scope.row['taskId']"
              :taskName="scope.row[col.prop]"
              :areaName="scope.row['areaName']"
            ></task-info>
            <!-- 其他数据直接显示 -->
            <span v-else>
              {{ scope.row[col.prop] || 0 }}
            </span>
          </template>
        </el-table-column>
      </x-table>
    </el-card>
    <countdown ref="timer" :table="$refs.table"></countdown>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import Countdown from '@/components/Page/Countdown'
import TaskInfo from '@/components/Table/CustomColumn/TaskInfoColumn'
import { tableHeightMixin } from '@/mixin'
import { deliverTaskInFansSpeedApi, optionsApi } from '@/api'
export default {
  components: {
    TaskInfo,
    Countdown,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
      columns: [],
      taskNameColors: {}, // 存储 taskName 对应的颜色类名
      taskConfigOptions: [],
      colorClasses: [
        'task-color-1',
        'task-color-2',
        'task-color-3',
        'task-color-4',
        'task-color-5',
        'task-color-6',
        'task-color-7',
        'task-color-8',
        'task-color-9',
        'task-color-10',
      ], // 预定义的颜色类名
    }
  },
  created() {},
  beforeRouteLeave(to, from, next) {
    this.$refs.timer.stopAutoRefresh() // 手动调用子组件方法
    next()
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getColumns() {
      this.$xloading.show()
      const res = await deliverTaskInFansSpeedApi.getTaskInFansColumns()
      if (res && res.code == 0) {
        this.columns = res.data
      } else this.$xMsgError(res.msg)
      this.$xloading.hide()
    },
    async getList(params) {
      this.$xloading.show()
      //await this.getColumns()
      params = Object.assign({}, params, this.queryParams)
      if (this.queryParams.date) {
        params.beginTime = this.queryParams.date[0]
        params.endTime = this.queryParams.date[1]
      }
      const res = await deliverTaskInFansSpeedApi.getList(params)
      this.columns = res.columns
      this.tableData = res

      // 重新分配颜色映射
      this.assignTaskColors()

      this.$xloading.hide()
    },
    async fetchTaskConfigOption() {
      const res = await optionsApi.getTaskConfigOptions({ areaId: this.queryParams.areaId })
      if (res && res.code == 0) {
        this.taskConfigOptions = res.data
      } else this.$xMsgError(res.msg)
    },
    reset() {
      const beginDate = dayjs().format('YYYY-MM-DD 00:00:00')
      const endDate = dayjs().add(1, 'day').format('YYYY-MM-DD 00:00:00')
      this.queryParams.date = [beginDate, endDate]
    },
    tableRowClassName({ row }) {
      // 根据 taskName 分配颜色
      const taskName = row.taskName
      if (!taskName) return ''

      // 如果这个 taskName 还没有分配颜色，就分配一个
      if (!this.taskNameColors[taskName]) {
        const usedColors = Object.values(this.taskNameColors)
        const availableColors = this.colorClasses.filter((color) => !usedColors.includes(color))

        if (availableColors.length > 0) {
          this.taskNameColors[taskName] = availableColors[0]
        } else {
          // 如果所有颜色都用完了，使用哈希算法分配
          const hash = this.hashCode(taskName)
          this.taskNameColors[taskName] =
            this.colorClasses[Math.abs(hash) % this.colorClasses.length]
        }
      }
      return this.taskNameColors[taskName]
    },

    // 简单的哈希函数，用于当颜色不够时的分配
    hashCode(str) {
      let hash = 0
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = (hash << 5) - hash + char
        hash = hash & hash // 转换为32位整数
      }
      return hash
    },

    // 为当前数据中的所有 taskName 分配颜色
    assignTaskColors() {
      if (!this.tableData.data) return

      // 获取所有唯一的 taskName
      const uniqueTaskNames = [
        ...new Set(this.tableData.data.map((row) => row.taskName).filter((name) => name)),
      ]

      // 清空现有的颜色映射
      this.taskNameColors = {}

      // 为每个 taskName 分配颜色
      uniqueTaskNames.forEach((taskName, index) => {
        if (index < this.colorClasses.length) {
          this.taskNameColors[taskName] = this.colorClasses[index]
        } else {
          // 如果 taskName 数量超过预定义颜色数量，使用哈希算法
          const hash = this.hashCode(taskName)
          this.taskNameColors[taskName] =
            this.colorClasses[Math.abs(hash) % this.colorClasses.length]
        }
      })
    },
  },
}
</script>

<style>
/* 样式1：调整字体大小、行高、间距 */
.table-cell-content {
  line-height: 0.5; /* 行高 */
}

/* 根据 taskName 的不同颜色样式 */
.task-color-1 {
  background-color: rgba(255, 250, 240, 0.6) !important; /* 浅米色 */
}

.task-color-2 {
  background-color: rgba(227, 242, 253, 0.6) !important; /* 浅蓝色 */
}

.task-color-3 {
  background-color: rgba(232, 245, 233, 0.6) !important; /* 浅绿色 */
}

.task-color-4 {
  background-color: rgba(255, 248, 225, 0.6) !important; /* 浅橙色 */
}

.task-color-5 {
  background-color: rgba(243, 229, 245, 0.6) !important; /* 浅紫色 */
}

.task-color-6 {
  background-color: rgba(224, 247, 250, 0.6) !important; /* 浅青色 */
}

.task-color-7 {
  background-color: rgba(255, 243, 224, 0.6) !important; /* 浅桃色 */
}

.task-color-8 {
  background-color: rgba(240, 248, 255, 0.6) !important; /* 浅天蓝色 */
}

.task-color-9 {
  background-color: rgba(245, 255, 245, 0.6) !important; /* 浅薄荷绿 */
}

.task-color-10 {
  background-color: rgba(255, 235, 238, 0.6) !important; /* 浅粉色 */
}

/* 鼠标悬停效果 */
.task-color-1:hover {
  background-color: rgba(255, 250, 240, 0.8) !important;
}

.task-color-2:hover {
  background-color: rgba(227, 242, 253, 0.8) !important;
}

.task-color-3:hover {
  background-color: rgba(232, 245, 233, 0.8) !important;
}

.task-color-4:hover {
  background-color: rgba(255, 248, 225, 0.8) !important;
}

.task-color-5:hover {
  background-color: rgba(243, 229, 245, 0.8) !important;
}

.task-color-6:hover {
  background-color: rgba(224, 247, 250, 0.8) !important;
}

.task-color-7:hover {
  background-color: rgba(255, 243, 224, 0.8) !important;
}

.task-color-8:hover {
  background-color: rgba(240, 248, 255, 0.8) !important;
}

.task-color-9:hover {
  background-color: rgba(245, 255, 245, 0.8) !important;
}

.task-color-10:hover {
  background-color: rgba(255, 235, 238, 0.8) !important;
}
</style>
