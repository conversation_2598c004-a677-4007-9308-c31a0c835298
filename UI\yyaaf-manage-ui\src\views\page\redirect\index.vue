<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="渠道">
          <x-select filterable v-model="queryParams.cId" url="/deliverChannel/options"></x-select>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['pageRedirect:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column label="页面标识/标题" align="center" min-width="250">
          <template v-slot="{ row }">
            <span>{{ row.id }}</span>
            <br />
            <span>{{ row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="cateName" label="地区" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ row.areaName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="渠道" align="center" min-width="200">
          <template v-slot="{ row }">
            <channel-info :cId="row.cId" :cName="row.cName"></channel-info>
          </template>
        </el-table-column>
        <el-table-column
          prop="pixelId"
          label="像素"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column prop="createOn" label="时间" align="center" min-width="160">
          <template v-slot="{ row }">
            <span>{{ row.createOn }}</span>
            <br />
            <span>{{ row.editTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.enable">启用</el-tag>
            <el-tag type="danger" size="medium" v-else>禁用</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="230" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleDeliverRedirectUrl(scope.row)"
              v-permission="['page:preview']"
              >链接</el-button
            >
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['pageRedirect:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['pageRedirect:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import ChannelInfo from '@/components/Table/CustomColumn/ChannelColumn'
import { tableHeightMixin } from '@/mixin'
import { pageRedirectApi } from '@/api'
import EditDialog from './edit'
export default {
  components: {
    ChannelInfo,
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await pageRedirectApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDeliverRedirectUrl(row) {
      this.$xloading.show()
      const res = await pageRedirectApi.deliverRedirectUrl(row.id)
      if (res.code == 0) {
        console.log(res.data)
        let url = res.data
        this.copyAccounts(url)
      }
      this.$xloading.hide()
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await pageRedirectApi.delPageRedirect(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    copyAccounts(text) {
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)
      textarea.select()
      try {
        document.execCommand('copy')
        this.$xMsgSuccess('复制成功！')
      } catch (err) {
        this.$xMsgError('复制失败，请手动复制')
      }
      document.body.removeChild(textarea)
    },
    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
