import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/fBAd/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/fBAd/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addFBAd(data) {
  return request({
    url: '/fBAd/add',
    method: 'post',
    data: data,
  })
}

export function editFBAd(data) {
  return request({
    url: '/fBAd/edit',
    method: 'post',
    data: data,
  })
}

export function delFBAd(id) {
  return request({
    url: '/fBAd/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function changeStatus(data) {
  return request({
    url: '/fBAd/changeStatus',
    method: 'post',
    data: data,
  })
}

//----------FBAd结束----------
