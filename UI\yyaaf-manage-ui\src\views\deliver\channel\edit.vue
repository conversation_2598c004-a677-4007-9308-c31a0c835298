<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-col :span="16">
          <el-form-item label="渠道标识" prop="id">
            <el-input :disabled="isDisabled" v-model="form.id" placeholder="请输入渠道标识" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="渠道名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入渠道名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :autosize="{ minRows: 6 }"
              v-model="form.remark"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用" prop="enable">
            <x-radio v-model="form.enable" button :options="enableOptions" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { enableOptions } from '@/utils/options'
import { deliverChannelApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      isDisabled: false,
      visible: false,
      loading: false,
      form: {
        enable: true,
      },
      rules: {
        name: [{ required: true, message: '请输入渠道名称', trigger: 'blur' }],
        id: [{ required: true, message: '请输入渠道标识', trigger: 'blur' }],
      },
      enableOptions,
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.isDisabled = false
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.isDisabled = true
      this.visible = true
      this.$xloading.show()
      const res = await deliverChannelApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {
        enable: true,
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          if (this.isDisabled) {
            const res = await deliverChannelApi.editDeliverChannel(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await deliverChannelApi.addDeliverChannel(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
