const mixin = {
  data() {
    return {
      tableHeight: 0,
    }
  },
  mounted() {
    const calcHeight = (options) => {
      const defaultOptions = {
        queryName: 'queryForm',
        toolbarName: 'toolbar',
        page: true, // 自否开启了分页
        refs: [], // 除 queryForm, toolbar 之外的组件，如果是el的组件，传值的格式为：$el-ref的名字
        value: 0, // 自定义要减去的高度
      }
      options = Object.assign({}, defaultOptions, options)
      const headerH = 50
      const containerPadding = 40
      const cardBoder = 2
      const mainPadding = 40
      let queryHeight = 0
      if (this.$refs[options.queryName]) {
        queryHeight = this.$refs[options.queryName].$el.offsetHeight
      }
      let toolbarH = 0
      if (this.$refs[options.toolbarName]) {
        // 加上15的外边距
        toolbarH = this.$refs[options.toolbarName].$el.offsetHeight + 15
      }
      let pageH = 0
      if (options.page) {
        pageH = 32
      }

      // 除 queryForm, toolbar 之外的组件的高度
      let refHeight = 0
      options.refs.forEach((t) => {
        const elPrefix = '$el-'
        if (t.startsWith(elPrefix)) {
          refHeight += this.$refs[t.substring(elPrefix.length)].$el.offsetHeight
        } else {
          refHeight += this.$refs[t].$el.offsetHeight
        }
      })
      // console.log(refHeight)

      const tableHeight =
        window.innerHeight -
        (headerH +
          containerPadding +
          mainPadding +
          cardBoder +
          queryHeight +
          toolbarH +
          pageH +
          1 +
          options.value +
          refHeight)
      this.tableHeight = tableHeight
    }
    window.onresize = () => {
      calcHeight(this.tableHeightOptions)
    }
    this.$nextTick(() => {
      calcHeight(this.tableHeightOptions)
    })
  },
}

export default mixin
