import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/fBAdCampaign/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/fBAdCampaign/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addFBAdCampaign(data) {
  return request({
    url: '/fBAdCampaign/add',
    method: 'post',
    data: data,
  })
}

export function editFBAdCampaign(data) {
  return request({
    url: '/fBAdCampaign/edit',
    method: 'post',
    data: data,
  })
}

export function delFBAdCampaign(id) {
  return request({
    url: '/fBAdCampaign/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function changeStatus(data) {
  return request({
    url: '/fBAdCampaign/changeStatus',
    method: 'post',
    data: data,
  })
}

//----------FBAdCampaign结束----------
