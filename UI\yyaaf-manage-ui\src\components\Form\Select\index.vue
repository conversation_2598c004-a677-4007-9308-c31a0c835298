<template>
  <el-select v-bind="$attrs" v-on="$listeners" :value="currentValue" @change="handleChange">
    <slot v-bind:options="optionData">
      <el-option
        v-for="item in optionData"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </slot>
  </el-select>
</template>

<script>
import request from '@/utils/request'
export default {
  inheritAttrs: false,
  props: {
    showDefault: {
      // 是否显示默认的选项（全部）
      type: Boolean,
      default: false,
    },
    options: Array, // 选项的数据，对象的属性为 { label: "xxx", value: xxx }
    url: String, // url，对象属性同上
    value: {
      type: [String, Number, Boolean],
    },
  },

  data() {
    return {
      optionData: [],
      currentValue: this.value,
    }
  },
  mounted() {
    // console.log(this.url)
    if (this.options) {
      this.updateOptionsData(this.options)
    }
    if (this.url) {
      this.loadOptions(this.url)
    }
  },
  watch: {
    options(newVal) {
      this.updateOptionsData(newVal)
    },
    url(newVal) {
      // console.log(newVal)
      this.loadOptions(this.url)
    },
    value(newVal) {
      // console.log(newVal)
      this.setCurrentValue(newVal)
    },
  },
  methods: {
    async loadOptions(url) {
      const res = await request({
        url: url,
        method: 'get',
      })
      if (res.code == 0) {
        this.updateOptionsData(res.data)
        // 选项加载完成后，重新设置当前值以确保正确显示
        this.$nextTick(() => {
          if (this.value !== undefined && this.value !== null && this.value !== '') {
            this.setCurrentValue(this.value)
          }
        })
      } else {
        console.error(`select组件获取数据失败【${url}】：${res.msg}`)
      }
    },
    handleChange(value) {
      // console.log('handleInput:', value)
      this.setCurrentValue(value)
      this.$emit('input', value)
    },
    setCurrentValue(value) {
      if (value == this.currentValue) {
        return
      }
      this.currentValue = value
    },
    updateOptionsData(options) {
      if (this.showDefault) {
        var defaultOptionItem = { label: '全部', value: -1 }
        this.optionData = [defaultOptionItem, ...options]
        return
      }
      this.optionData = options
    },
  },
}
</script>

<style></style>
