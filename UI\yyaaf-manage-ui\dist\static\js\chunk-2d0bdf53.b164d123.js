(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0bdf53"],{"2de0":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container",attrs:{align:"center"}},[r("h2",[e._v(e._s(e.text))])])},i=[],o=(r("8e6e"),r("ac6a"),r("456d"),r("96cf"),r("1da1")),a=r("ade3"),c=(r("a481"),r("2f62"));function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){Object(a["a"])(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var p={data:function(){return{text:"登录中...",loading:!1}},mounted:function(){var e=this.$router.history.current.query;if(!e.code)return this.$xMsgError("无效的code，请重试"),void this.$router.replace({path:"/login"});this.getToken(e)},methods:u(u({},Object(c["b"])(["OAuthLogin"])),{},{getToken:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(t){var r=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.prev=1,e.next=4,this.OAuthLogin(t).then((function(e){r.$router.replace({path:"/"})}));case 4:this.$xloading.hide(),e.next=12;break;case 7:e.prev=7,e.t0=e["catch"](1),this.$xMsgError("登录失败，"+e.t0),this.$router.replace({path:"/login"}),this.$xloading.hide();case 12:case"end":return e.stop()}}),e,this,[[1,7]])})));function t(t){return e.apply(this,arguments)}return t}()})},h=p,l=r("2877"),d=Object(l["a"])(h,n,i,!1,null,null,null);t["default"]=d.exports}}]);