<template>
  <div class="welcome-container">
    <div class="welcome-header">
      <img :src="avatarUrl" alt="Avatar" class="avatar" />
      <h1>欢迎回来，{{ name }}！</h1>
      <p class="welcome-message">很高兴见到你，祝你今天心情愉快！</p>
      <!-- 版本信息 -->
      <p class="version-info">版本号：{{ upgradeInfo.verName }} ({{ upgradeInfo.verCode }})</p>
    </div>
    <div class="button-group">
      <el-button type="primary" plain round icon="el-icon-refresh" @click="syncUserData"
        >同步账户</el-button
      >
      <el-button type="success" plain round icon="el-icon-user" @click="center">个人中心</el-button>
      <el-button type="danger" plain round icon="el-icon-switch-button" @click="logout"
        >退出登录</el-button
      >
    </div>
  </div>
</template>

<script>
import * as message from '@/utils/message'
import { mapGetters } from 'vuex'
import avatarPlaceholder from '@/assets/images/avatar_placeholder.png'

export default {
  data() {
    return {
      avatarUrl: avatarPlaceholder,
      upgradeInfo: {
        verName: '0.0.0',
        verCode: 0,
      },
    }
  },
  computed: {
    ...mapGetters(['avatar', 'name', 'userId']),
  },
  created() {
    let query = this.$router.history.current.query
    if (query.verName) this.upgradeInfo.verName = query.verName
    if (query.verCode) this.upgradeInfo.verCode = query.verCode
  },
  mounted() {
    if (this.avatar) {
      this.avatarUrl = this.avatar
    }

    let userData = {
      name: this.name,
      userId: this.userId,
    }
    message.postMessage({ type: 'UserData', data: userData })

    //this.listenerMessage()
  },
  methods: {
    center() {
      let tabData = {
        url: window.location.origin,
      }
      message.postMessage({ type: 'OpenTab', data: tabData })
    },
    async logout() {
      this.$xloading.show()
      await this.$store.dispatch('Logout')
      message.postMessage({ type: 'Logout' })
      this.$xloading.hide()
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    async syncUserData() {
      this.$xMsgInfo('账户开始同步，请等候同步通知！')
      message.postMessage({ type: 'SyncUserData' })
    },
    // chrome的插件中打开这个页面是拿不到 window.parent.postMessage({ type: 'SyncUserDataResponse', data: response }, '*'); 过来的数据的。
    /*listenerMessage() {
      window.addEventListener(
        'message',
        (event) => {
          // 确保数据来源安全 (建议替换 '*' 为特定的源)
         console.log("Received message:", event.data);
          if (event.data.type === 'SyncUserDataResponse') {
            // console.log("收到 Content Script 发送回来的数据：", event.data)
            var res = event.data
            if (res.code == 1) {
              this.$xMsgSuccess('同步账户成功！')
            }
            return
          }
        },
        false
      )
    },*/
  },
}
</script>

<style scoped>
/* 页面容器 */
.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: #fff;
  font-family: 'Arial', sans-serif;
  text-align: center;
}

/* 欢迎头部区域 */
.welcome-header {
  margin-bottom: 30px;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin-bottom: 15px;
  border: 3px solid #fff;
}

h1 {
  margin: 0;
  font-size: 32px;
  font-weight: bold;
}

.welcome-message {
  font-size: 18px;
  margin-top: 10px;
  color: #dbeafe;
}

.version-info {
  font-size: 16px;
  margin-top: 10px;
  color: #dbeafe;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 20px;
}

.el-button {
  font-size: 16px;
  padding: 10px 15px;
}
</style>
