<template>
  <div class="app-container">
    <div style="margin: 0 0 20px 0" v-if="isShowUpgradeInfo">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" style="text-align: center; padding: 20px; border-radius: 10px">
            <p style="font-weight: bold; font-size: 18px; color: #409eff">
              当前最新插件版本为 {{ upgradeInfo.verName }} ,请更新插件！
            </p>
            <p style="font-size: 14px; color: #606266">请及时更新以获取最新功能和优化体验。</p>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <!-- 🌟 美化的顶部提示 -->
    <div class="info-box">📢 <span class="info-text">每日投放渠道主号信息</span></div>
    <el-row :gutter="20">
      <el-col :span="8" v-for="(data, index) in list" :key="index">
        <el-card class="card-box">
          <!-- 头部信息 -->
          <div class="card-header">
            <div>
              <span class="channelId">🔹 渠道 ID：{{ data.cId }}</span>
              <span class="subtext">📍 地区：{{ data.areaName }}</span>
              <span class="subtext">📌 任务：{{ data.taskName }}</span>
            </div>
            <el-button
              class="copy-btn"
              type="primary"
              plain
              size="small"
              @click="copyAccounts(data.accountList)"
            >
              复制主号
            </el-button>
          </div>
          <!-- 账号列表 -->
          <div class="account-list">
            <el-tag
              v-for="account in data.accountList"
              :key="account"
              class="account-tag"
              type="success"
            >
              {{ account }}
            </el-tag>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { deliverDashboardApi } from '@/api'
import * as message from '@/utils/message'
import { mapGetters } from 'vuex'
import Cookies from 'js-cookie'

export default {
  name: 'Dashboard',
  data() {
    return {
      isShowUpgradeInfo: false,
      upgradeInfo: {
        verName: '1.0.0',
        verCode: 1000,
      },
      list: {},
    }
  },
  computed: {
    ...mapGetters(['name', 'userId']),
  },
  created() {
    var user = {
      userId: this.userId,
      name: this.name,
    }
    Cookies.set('yyaaf_userdata', user)
  },
  async mounted() {
    this.listenerMessage()
    message.postMessage({ type: 'PluginVersion', data: {} })
    await this.getChannelUserList()
  },
  beforeDestroy() {
    window.removeEventListener('message', this.handleMessage)
  },
  methods: {
    quickStart(item) {
      if (item.href) {
        this.$router.push({ path: item.href })
      }
    },
    listenerMessage() {
      window.addEventListener(
        'message',
        (event) => {
          // 确保数据来源安全 (建议替换 '*' 为特定的源)
          // console.log("Received message:", event.data);
          if (event.data.type === 'PluginVersionResponse') {
            //console.log("收到 Content Script 发送回来的数据：", event.data.data)
            var pluginInfo = event.data.data
            if (pluginInfo.verCode < this.upgradeInfo.verCode) {
              this.isShowUpgradeInfo = true
            }
            return
          }
        },
        false
      )
    },
    async getChannelUserList() {
      this.$xloading.show()
      const res = await deliverDashboardApi.getChannelUserList()
      if (res && res.code == 0) {
        this.list = res.data
      } else this.$xMsgError(res.msg)
      this.$xloading.hide()
    },
    copyAccounts(accounts) {
      const text = accounts.join('\n')
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)
      textarea.select()
      try {
        document.execCommand('copy')
        this.$xMsgSuccess('复制成功！')
      } catch (err) {
        console.error('复制失败:', err)
        this.$xMsgError('复制失败，请手动复制')
      }
      document.body.removeChild(textarea)
    },
  },
}
</script>

<style lang="scss" scoped>
/* 页面背景 */
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
}

/* 让列间距更均匀 */
.el-row {
  margin: 0;
}

/* 卡片样式 */
.card-box {
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease-in-out;
  background: #ffffff;
  border: none;
  padding: 15px;
  margin-bottom: 20px;
}

.card-box:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* 头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #eef2f6;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 10px;
}

/* 渠道 ID */
.channelId {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  background: #ecf5ff;
  padding: 6px 12px;
  border-radius: 8px;
  display: inline-block;
  margin-bottom: 6px;
}

/* 副标题 */
.subtext {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 4px;
}

/* 复制按钮 */
.copy-btn {
  font-size: 12px;
  border-radius: 6px;
  padding: 6px 10px;
  transition: background-color 0.2s ease-in-out;
}

.copy-btn:hover {
  background-color: #409eff;
  color: #fff;
}

/* 账号列表 */
.account-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 10px 0;
}

/* 账号标签 */
.account-tag {
  font-size: 14px;
  border-radius: 6px;
  background-color: #e1f3d8;
  color: #2f7e40;
  border: none;
}

/* 页面背景 */
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
}

/* 🌟 顶部提示栏 */
.info-box {
  background: #eef5ff;
  padding: 12px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

/* 提示文字 */
.info-text {
  margin-left: 6px;
}
</style>
