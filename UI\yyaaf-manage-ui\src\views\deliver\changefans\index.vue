<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="地区">
          <x-select
            show-default
            v-model="queryParams.areaId"
            url="/deliverArea/options"
            @change="fetchTaskConfigOption"
          ></x-select>
        </el-form-item>
        <el-form-item label="任务">
          <x-select
            show-default
            v-model="queryParams.taskConfigId"
            :options="taskConfigOptions"
          ></x-select>
        </el-form-item>
        <el-form-item label="主号">
          <el-input v-model="queryParams.account"></el-input>
        </el-form-item>
        <el-form-item label="类型">
          <x-select show-default v-model="queryParams.type" :options="typeOptions"></x-select>
        </el-form-item>
        <el-form-item label="调粉时段">
          <el-date-picker
            v-model="queryParams.date"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar"> </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="taskName" label="任务名称/地区" align="center" min-width="250">
          <template v-slot="{ row }">
            <task-info
              :taskId="row.taskId"
              :taskName="row.taskName"
              :areaName="row.areaName"
            ></task-info>
          </template>
        </el-table-column>
        <el-table-column prop="account" label="主号" align="center" min-width="150">
          <template v-slot="{ row }">
            <el-link type="primary" :underline="false" @click="handleGotoCounter(row.account)">
              <div>{{ row.account }}</div>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="primary" size="medium" v-if="row.type == 0">调粉</el-tag>
            <el-tag type="warning" size="medium" v-else-if="row.type == 1">封号</el-tag>
            <el-tag type="warning" size="medium" v-else-if="row.type == 3">未找到</el-tag>
            <el-tag type="warning" size="medium" v-else-if="row.type == 4">强制下线</el-tag>
            <el-tag type="warning" size="medium" v-else-if="row.type == 5">换号</el-tag>
            <el-tag type="info" size="medium" v-else>恢复</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="调粉渠道" align="center" min-width="200">
          <template v-slot="{ row }">
            <channel-info :cId="row.scId" :cName="row.scName"></channel-info>
          </template>
        </el-table-column>
        <el-table-column label="目标渠道" align="center" min-width="200">
          <template v-slot="{ row }">
            <channel-info :cId="row.tcId" :cName="row.tcName"></channel-info>
          </template>
        </el-table-column>
        <el-table-column
          prop="newFans"
          label="新粉数量"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="repeatFans"
          label="重粉数量"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column prop="price" label="价格" align="center" min-width="100"></el-table-column>
        <el-table-column
          prop="changeTime"
          label="调粉时间"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="adminName"
          label="管理员"
          align="center"
          min-width="100"
        ></el-table-column>
      </x-table>
    </el-card>
    <countdown ref="timer" :table="$refs.table"></countdown>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import Countdown from '@/components/Page/Countdown'
import TaskInfo from '@/components/Table/CustomColumn/TaskInfoColumn'
import ChannelInfo from '@/components/Table/CustomColumn/ChannelColumn'
import { tableHeightMixin } from '@/mixin'
import { deliverTaskChangeFansApi, optionsApi } from '@/api'
export default {
  components: {
    TaskInfo,
    ChannelInfo,
    Countdown,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        type: -1,
      },
      loading: false,
      tableData: {},
      typeOptions: [
        { label: '调粉', value: 0 },
        { label: '封号', value: 1 },
        { label: '恢复', value: 2 },
        { label: '未找到', value: 3 },
        { label: '强制下线', value: 4 },
        { label: '换号', value: 5 },
      ],
      taskConfigOptions: [],
    }
  },
  created() {},
  beforeRouteLeave(to, from, next) {
    this.$refs.timer.stopAutoRefresh() // 手动调用子组件方法
    next()
  },
  methods: {
    queryReset() {
      this.queryParams = {
        type: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (this.queryParams.date) {
        params.beginTime = this.queryParams.date[0]
        params.endTime = this.queryParams.date[1]
      }
      const res = await deliverTaskChangeFansApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    async fetchTaskConfigOption() {
      const res = await optionsApi.getTaskConfigOptions({ areaId: this.queryParams.areaId })
      if (res && res.code == 0) {
        this.taskConfigOptions = res.data
      } else this.$xMsgError(res.msg)
    },
    reset() {
      const beginDate = dayjs().format('YYYY-MM-DD 00:00:00')
      const endDate = dayjs().add(1, 'day').format('YYYY-MM-DD 00:00:00')
      this.queryParams.date = [beginDate, endDate]
    },
    handleGotoCounter(account) {
      window.open(`/counter/counterorderdaterecords?account=${account}`, '_blank')
    },
  },
}
</script>
