<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" :label-width="labelWidth" size="medium">
      <!-- 名称 -->
      <el-col :span="24">
        <el-form-item label="动作名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入此动作的名称"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="是否登录" prop="needLogin">
          <el-radio-group v-model="form.needLogin">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
          <span class="extra">是否需要登录才能执行此动作</span>
        </el-form-item>
      </el-col>
      <!-- <el-col :span="24" >
        <el-form-item label="是否登录" prop="needLogin">
          <el-radio-group v-model="form.needLogin">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
          <span class="extra">是否需要登录才能执行此动作</span>
        </el-form-item>
      </el-col> -->

      <el-col :span="24">
        <el-form-item label="操作类型" prop="operaType">
          <x-radio
            v-model="form.operaType"
            :options="operaTypeOptions"
            @change="onOperaTypeChange"
          ></x-radio>
        </el-form-item>
      </el-col>
      <!-- 路由 -->
      <template v-if="form.operaType == 1">
        <el-col :span="24">
          <el-form-item label="路由类型" prop="routeType">
            <x-radio v-model="form.routeType" url="/options/routeTypes" v-slot="{ options }">
              <el-col :span="6" v-for="item in options" :key="item.id" class="mb-10">
                <el-radio :label="item.value">{{ item.label }}</el-radio>
              </el-col>
            </x-radio>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="路由数据">
            <el-input v-model="form.data" placeholder="请输入数据"></el-input>
          </el-form-item>
        </el-col>
      </template>

      <!-- 打开网页 -->
      <template v-if="form.operaType == 2">
        <el-col :span="24">
          <el-form-item label="是否弹窗网页" prop="isPop">
            <el-radio-group v-model="form.isPop">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="链接" prop="url">
            <el-input v-model="form.url" placeholder="请输入链接"></el-input>
          </el-form-item>
        </el-col>
      </template>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    labelWidth: {
      type: String,
      default: '100px',
    },
  },
  data() {
    return {
      form: {},
      rules: {
        operaType: [{ required: true, message: '请选择动作类型', trigger: 'blur' }],
        routeType: [{ required: true, message: '请选择路由类型', trigger: 'blur' }],
        isPop: [{ required: true, message: '请选择是否弹窗网页', trigger: 'blur' }],
        url: [{ required: true, message: '请输入链接', trigger: 'blur' }],
      },
      operaTypeOptions: [
        { label: '无', value: -1 },
        { label: '路由', value: 1 },
        { label: '打开页面', value: 2 },
      ],
    }
  },
  methods: {
    onOperaTypeChange(value) {
      // console.log(value)
      // this.validate()
      this.$refs.form.clearValidate()
    },
    async validate() {
      try {
        let validate = await this.$refs['form'].validate()
        // 组装数据
        let data = Object.assign({}, this.form)
        console.log(data)
        let opera
        if (data.operaType == 1) {
          const route = {
            routeType: data.routeType,
            data: data.data,
          }
          opera = { route }
        } else if (data.operaType == 2) {
          const openWeb = {
            isPop: data.isPop,
            url: data.url,
          }
          opera = { openWeb }
        }
        data.opera = opera
        return data
      } catch (error) {
        return undefined
      }
    },
    async submitForm() {
      return this.validate()
    },
  },
}
</script>

<style></style>
