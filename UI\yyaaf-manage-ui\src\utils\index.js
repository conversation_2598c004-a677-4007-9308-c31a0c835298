/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"')
        .replace(/\+/g, ' ') +
      '"}'
  )
}

/**
 * 表单重置
 * @param {String} refName 表单ref
 */
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields()
  }
}

export function getUrlParams(url) {
  if (typeof url == 'undefined') {
    url = decodeURI(location.search)
  } else {
    url = '?' + url.split('?')[1]
  }
  // console.log(url)
  let params = {}
  if (url.indexOf('?') != -1) {
    let paramsStr = url.substring(1)
    const strs = paramsStr.split('&')
    strs.forEach((t) => {
      const kv = t.split('=')
      params[kv[0]] = decodeURI(kv[1])
    })
  }
  return params
}

/**
 * 将对象转换成url参数的格式，key=value&key1=value1
 * @param {Object} params 查询参数
 */
export function convertParamsToUrl(params) {
  if (typeof params == 'undefined' || params == null) {
    return
  }
  let urlParams = []
  Object.keys(params).forEach((key) => {
    urlParams.push(`${key}=${params[key]}`)
  })
  return urlParams.join('&')
}

export function parseDateFromTimestamp(timestamp) {
  return new Date(timestamp * 1000)
}

export function randomString(length = 16) {
  return Array.from({ length }, () => Math.floor(Math.random() * 36).toString(36)).join('')
}

/**
 * 获取本地时间的 YYYY-MM-DD 格式日期字符串。
 * @returns {string} 例如："2023-10-28"
 */
export function getLocalISODateString() {
  const date = new Date()
  const year = date.getFullYear()
  // getMonth() 返回 0-11，所以需要 +1。
  // padStart(2, '0') 用于确保月份和日期是两位数，例如 "05" 而不是 "5"。
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
