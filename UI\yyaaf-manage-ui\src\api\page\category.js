import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/category/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/category/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addCategory(data) {
  return request({
    url: '/category/add',
    method: 'post',
    data: data,
  })
}

export function editCategory(data) {
  return request({
    url: '/category/edit',
    method: 'post',
    data: data,
  })
}

export function delCategory(id) {
  return request({
    url: '/category/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function getCategoryOptions() {
  return request({
    url: '/category/options',
    method: 'get',
  })
}

//----------Category结束----------
