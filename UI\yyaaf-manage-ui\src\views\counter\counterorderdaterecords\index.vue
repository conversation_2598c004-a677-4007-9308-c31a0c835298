<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="主号">
          <el-input v-model="queryParams.account" placeholder="请输入用户主号" />
        </el-form-item>
        <el-form-item label="工单标题">
          <el-input v-model="queryParams.title" placeholder="请输入工单标题" />
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.createTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="日期"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!--<el-row ref="toolbar" class="table-toolbar">
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd" v-permission="['counterOrderDateRecords:add']">新增</el-button>
        </el-row> -->

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="date" label="日期" align="center" min-width="100">
          <template v-slot="{ row }">
            <span>{{ row.date.slice(0, 10) }}</span>
          </template>
        </el-table-column>
        <!--<el-table-column prop="sId" label="sId" align="center" min-width="100"></el-table-column>
          <el-table-column prop="workId" label="workId" align="center" min-width="100"></el-table-column>-->
        <el-table-column label="头像" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-image
              fit="contain"
              style="width: 35px; height: 35px"
              :src="row.headImg"
              :preview-src-list="[row.headImg]"
            ></el-image>
          </template>
        </el-table-column>
        <el-table-column
          prop="account"
          label="主号"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="nickName"
          label="昵称"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column
          prop="seatName"
          label="席位"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column prop="title" label="工单" align="center" min-width="150"></el-table-column>
        <el-table-column
          prop="platform"
          label="平台"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column label="在线状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.isOnline">在线</el-tag>
            <el-tag type="danger" size="medium" v-else>离线</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="分配状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.isAllocation">开启</el-tag>
            <el-tag type="danger" size="medium" v-else>关闭</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="已完成/当日目标" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ row.dayNewFans }} / {{ row.singleDayTarget }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已完成/总目标" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ row.newFans }} / {{ row.totalTarget }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="dayRepeatFans"
          label="本工单当日重粉数"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column
          prop="repeatFans"
          label="本工单重粉数"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column
          prop="lastOfflineTime"
          label="上次离线时间"
          align="center"
          min-width="200"
        ></el-table-column>

        <!--<el-table-column label="操作" width="160" align="center">
            <template slot-scope="scope">
              <el-button size="mini" 
                type="warning"
                @click="handleEdit(scope.row)"
                v-permission="['counterOrderDateRecords:edit']"
              >修改</el-button>
              <el-button size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
                v-permission="['counterOrderDateRecords:delete']"
              >删除</el-button>
            </template>
          </el-table-column>-->
      </x-table>
    </el-card>
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { counterOrderDateRecordsApi } from '@/api'
import { getLocalISODateString } from '@/utils/index'

export default {
  components: {},
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        account: null,
        createTime: getLocalISODateString(),
      },
      loading: false,
      tableData: {},
    }
  },
  created() {
    const query = this.$router.history.current.query
    if (query.account) {
      this.queryParams.account = query.account
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {
        createTime: getLocalISODateString(),
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (params.createTime) {
        params.date = params.createTime
        delete params.createTime
      }
      const res = await counterOrderDateRecordsApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },

    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
