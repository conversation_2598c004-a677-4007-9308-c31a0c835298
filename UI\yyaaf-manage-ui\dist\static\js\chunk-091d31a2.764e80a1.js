(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-091d31a2"],{3347:function(t,e,r){},"3fa5":function(t,e,r){"use strict";function n(t){window.parent.postMessage(t,"*")}r.d(e,"a",(function(){return n}))},c66f:function(t,e,r){"use strict";r("3347")},f86d:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"welcome-container"},[r("div",{staticClass:"welcome-header"},[r("img",{staticClass:"avatar",attrs:{src:t.avatarUrl,alt:"Avatar"}}),t._v(" "),r("h1",[t._v("欢迎回来，"+t._s(t.name)+"！")]),t._v(" "),r("p",{staticClass:"welcome-message"},[t._v("很高兴见到你，祝你今天心情愉快！")]),t._v(" "),r("p",{staticClass:"version-info"},[t._v("版本号："+t._s(t.upgradeInfo.verName)+" ("+t._s(t.upgradeInfo.verCode)+")")])]),t._v(" "),r("div",{staticClass:"button-group"},[r("el-button",{attrs:{type:"primary",plain:"",round:"",icon:"el-icon-refresh"},on:{click:t.syncUserData}},[t._v("同步账户")]),t._v(" "),r("el-button",{attrs:{type:"success",plain:"",round:"",icon:"el-icon-user"},on:{click:t.center}},[t._v("个人中心")]),t._v(" "),r("el-button",{attrs:{type:"danger",plain:"",round:"",icon:"el-icon-switch-button"},on:{click:t.logout}},[t._v("退出登录")])],1)])},a=[],o=(r("8e6e"),r("ac6a"),r("456d"),r("96cf"),r("1da1")),c=(r("7f7f"),r("ade3")),s=r("3fa5"),i=r("2f62"),u=r("fae4"),p=r.n(u);function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach((function(e){Object(c["a"])(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var d={data:function(){return{avatarUrl:p.a,upgradeInfo:{verName:"0.0.0",verCode:0}}},computed:l({},Object(i["c"])(["avatar","name","userId"])),created:function(){var t=this.$router.history.current.query;t.verName&&(this.upgradeInfo.verName=t.verName),t.verCode&&(this.upgradeInfo.verCode=t.verCode)},mounted:function(){this.avatar&&(this.avatarUrl=this.avatar);var t={name:this.name,userId:this.userId};s["a"]({type:"UserData",data:t})},methods:{center:function(){var t={url:window.location.origin};s["a"]({type:"OpenTab",data:t})},logout:function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.$xloading.show(),t.next=3,this.$store.dispatch("Logout");case 3:s["a"]({type:"Logout"}),this.$xloading.hide(),this.$router.push("/login?redirect=".concat(this.$route.fullPath));case 6:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),syncUserData:function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.$xMsgInfo("账户开始同步，请等候同步通知！"),s["a"]({type:"SyncUserData"});case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()}},v=d,h=(r("c66f"),r("2877")),g=Object(h["a"])(v,n,a,!1,null,"2cfb8568",null);e["default"]=g.exports}}]);