<template>
  <div class="app-container">
    <el-card>
      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['deliverChannelUser:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="id" label="id" align="center" min-width="100"></el-table-column>
        <el-table-column prop="userName" label="渠道账号" align="center" min-width="150">
          <template slot-scope="scope">
            <el-link
              type="primary"
              :underline="false"
              @click="handleCustomerLogin(scope.row.userName)"
            >
              <div>{{ scope.row.userName }}</div>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="nickName"
          label="渠道名称"
          align="center"
          min-width="200"
        ></el-table-column>
        <!--<el-table-column prop="password" label="password" align="center" width="100"></el-table-column>-->
        <el-table-column prop="enable" label="状态" align="center" min-width="100">
          <template slot-scope="{ row }">
            <el-tag type="success" size="medium" v-if="row.enable">是</el-tag>
            <el-tag type="danger" size="medium" v-else>否</el-tag>
          </template>
        </el-table-column>
        <!--<el-table-column prop="channelIds" label="channelIds" align="center" width="100"></el-table-column>-->

        <el-table-column
          prop="addTime"
          label="添加时间"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="editTime"
          label="修改时间"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column label="操作" width="260" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              v-permission="['deliverChannelUser:resetPassword']"
              @click="handleResetPassword(scope.row)"
              >重置密码</el-button
            >
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['deliverChannelUser:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['deliverChannelUser:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { deliverChannelUserApi } from '@/api'
import EditDialog from './edit'
export default {
  components: {
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await deliverChannelUserApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await deliverChannelUserApi.deldeliverChannelUser(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    async handleResetPassword(row) {
      this.$confirm('是否确认重置该用户的密码', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await deliverChannelUserApi.resetPassword(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('重置密码成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },

    async handleCustomerLogin(userName) {
      const res = await deliverChannelUserApi.customerLogin(userName)
      if (res.code == 0) {
        var url = `http://customer.729183.xyz/adminlogin?username=${userName}&token=${res.data}`
        window.open(url)
      } else {
        this.$xMsgError('操作失败！' + res.msg)
      }
    },
  },
}
</script>
