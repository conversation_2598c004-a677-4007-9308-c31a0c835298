import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/template/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/template/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function spiderTemplate(data) {
  return request({
    url: '/template/spider',
    method: 'post',
    data: data,
    timeout: 1000 * 60 * 5,
  })
}

export function addTemplate(data) {
  return request({
    url: '/template/add',
    method: 'post',
    data: data,
  })
}

export function editTemplate(data) {
  return request({
    url: '/template/edit',
    method: 'post',
    data: data,
  })
}

export function delTemplate(id) {
  return request({
    url: '/template/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function preview(id) {
  return request({
    url: '/template/preview',
    method: 'get',
    params: {
      id,
    },
  })
}

export function getTemplateOptions(params) {
  return request({
    url: '/template/options',
    method: 'get',
    params: params,
  })
}

//----------Template结束----------
