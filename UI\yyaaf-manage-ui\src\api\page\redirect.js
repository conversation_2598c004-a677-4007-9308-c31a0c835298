import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/redirect/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/redirect/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addPageRedirect(data) {
  return request({
    url: '/redirect/add',
    method: 'post',
    data: data,
  })
}

export function editPageRedirect(data) {
  return request({
    url: '/redirect/edit',
    method: 'post',
    data: data,
  })
}

export function delPageRedirect(id) {
  return request({
    url: '/redirect/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function deliverRedirectUrl(id) {
  return request({
    url: '/redirect/deliverRedirectUrl',
    method: 'get',
    params: {
      id,
    },
  })
}

//----------PageRedirect结束----------
