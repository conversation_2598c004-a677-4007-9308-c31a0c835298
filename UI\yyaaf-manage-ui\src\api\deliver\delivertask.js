import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/deliverTask/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/deliverTask/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addDeliverTask(data) {
  return request({
    url: '/deliverTask/add',
    method: 'post',
    data: data,
  })
}

export function editDeliverTask(data) {
  return request({
    url: '/deliverTask/edit',
    method: 'post',
    data: data,
  })
}

export function delDeliverTask(id) {
  return request({
    url: '/deliverTask/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function changeFans(data) {
  return request({
    url: '/deliverTask/changeFans',
    method: 'post',
    data: data,
  })
}

export function changeEnable(id, enable) {
  return request({
    url: '/deliverTask/changeEnable',
    method: 'post',
    data: {
      id,
      enable,
    },
  })
}

export function finishTask(id, force) {
  return request({
    url: '/deliverTask/finishTask',
    method: 'post',
    data: {
      id,
      force,
    },
  })
}

export function stats(date) {
  return request({
    url: '/deliverTask/stats',
    method: 'post',
    data: {
      date,
    },
  })
}

//----------DeliverTask结束----------
