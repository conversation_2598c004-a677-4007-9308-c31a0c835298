(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7bcacc10"],{"1e4b":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[e.isShowUpgradeInfo?n("div",{staticStyle:{margin:"0 0 20px 0"}},[n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:24}},[n("el-card",{staticStyle:{"text-align":"center",padding:"20px","border-radius":"10px"},attrs:{shadow:"hover"}},[n("p",{staticStyle:{"font-weight":"bold","font-size":"18px",color:"#409eff"}},[e._v("\n            当前最新插件版本为 "+e._s(e.upgradeInfo.verName)+" ,请更新插件！\n          ")]),e._v(" "),n("p",{staticStyle:{"font-size":"14px",color:"#606266"}},[e._v("请及时更新以获取最新功能和优化体验。")])])],1)],1)],1):e._e(),e._v(" "),e._m(0),e._v(" "),n("el-row",{attrs:{gutter:20}},e._l(e.list,(function(t,r){return n("el-col",{key:r,attrs:{span:8}},[n("el-card",{staticClass:"card-box"},[n("div",{staticClass:"card-header"},[n("div",[n("span",{staticClass:"channelId"},[e._v("🔹 渠道 ID："+e._s(t.cId))]),e._v(" "),n("span",{staticClass:"subtext"},[e._v("📍 地区："+e._s(t.areaName))]),e._v(" "),n("span",{staticClass:"subtext"},[e._v("📌 任务："+e._s(t.taskName))])]),e._v(" "),n("el-button",{staticClass:"copy-btn",attrs:{type:"primary",plain:"",size:"small"},on:{click:function(n){return e.copyAccounts(t.accountList)}}},[e._v("\n            复制主号\n          ")])],1),e._v(" "),n("div",{staticClass:"account-list"},e._l(t.accountList,(function(t){return n("el-tag",{key:t,staticClass:"account-tag",attrs:{type:"success"}},[e._v("\n            "+e._s(t)+"\n          ")])})),1)])],1)})),1)],1)},a=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"info-box"},[e._v("📢 "),n("span",{staticClass:"info-text"},[e._v("每日投放渠道主号信息")])])}],s=(n("8e6e"),n("ac6a"),n("456d"),n("96cf"),n("1da1")),c=(n("7f7f"),n("ade3")),i=n("365c"),o=n("3fa5"),u=n("2f62"),l=n("a78e"),d=n.n(l);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){Object(c["a"])(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var h={name:"Dashboard",data:function(){return{isShowUpgradeInfo:!1,upgradeInfo:{verName:"1.0.0",verCode:1e3},list:{}}},computed:f({},Object(u["c"])(["name","userId"])),created:function(){var e={userId:this.userId,name:this.name};d.a.set("yyaaf_userdata",e)},mounted:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.listenerMessage(),o["a"]({type:"PluginVersion",data:{}}),e.next=4,this.getChannelUserList();case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),beforeDestroy:function(){window.removeEventListener("message",this.handleMessage)},methods:{quickStart:function(e){e.href&&this.$router.push({path:e.href})},listenerMessage:function(){var e=this;window.addEventListener("message",(function(t){if("PluginVersionResponse"!==t.data.type);else{var n=t.data.data;n.verCode<e.upgradeInfo.verCode&&(e.isShowUpgradeInfo=!0)}}),!1)},getChannelUserList:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,i["i"].getChannelUserList();case 3:t=e.sent,t&&0==t.code?this.list=t.data:this.$xMsgError(t.msg),this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),copyAccounts:function(e){var t=e.join("\n"),n=document.createElement("textarea");n.value=t,document.body.appendChild(n),n.select();try{document.execCommand("copy"),this.$xMsgSuccess("复制成功！")}catch(r){console.error("复制失败:",r),this.$xMsgError("复制失败，请手动复制")}document.body.removeChild(n)}}},v=h,g=(n("ca1e"),n("2877")),m=Object(g["a"])(v,r,a,!1,null,"247d8d10",null);t["default"]=m.exports},"3a7b":function(e,t,n){},"3fa5":function(e,t,n){"use strict";function r(e){window.parent.postMessage(e,"*")}n.d(t,"a",(function(){return r}))},ca1e:function(e,t,n){"use strict";n("3a7b")}}]);