import * as loginApi from '@/api/system/login'
import * as menuApi from '@/api/system/menu'
import * as roleApi from '@/api/system/role'
import * as sysUserApi from '@/api/system/user'
import * as authApi from '@/api/system/auth'
import * as redisApi from '@/api/system/redis'
import * as systemNoticeApi from '@/api/system/notice'
import * as userdatapermissionApi from '@/api/system/userdatapermission'

import * as fileApi from '@/api/file'
import * as optionsApi from '@/api/options'

import * as fBUserApi from '@/api/fb/fbuser'
import * as fBBusinessesApi from '@/api/fb/fbbusiness'
import * as fBAdAccountApi from '@/api/fb/fbadaccount'

import * as fBAdApi from '@/api/fb/fbad'
import * as fBAdSetApi from '@/api/fb/fbadset'
import * as fBAdCampaignApi from '@/api/fb/fbadcampaign'
import * as fBadinsightsApi from '@/api/stats/fbadinsights'

import * as counterOrderConfigApi from '@/api/counter/counterorderconfig'
import * as counterOrderDateRecordsApi from '@/api/counter/counterorderdaterecords'

import * as deliverDashboardApi from '@/api/deliver/deliverdashboard'
import * as deliverChannelApi from '@/api/deliver/deliverchannel'
import * as deliverTaskApi from '@/api/deliver/delivertask'
import * as deliverAreaApi from '@/api/deliver/deliverarea'
import * as deliverStatsApi from '@/api/deliver/deliverstats'
import * as deliverTaskInFansSpeedApi from '@/api/deliver/delivertaskinfansspeed'
import * as deliverTaskChangeFansApi from '@/api/deliver/delivertaskchangefans'
import * as deliverTaskChannelAccountApi from '@/api/deliver/delivertaskchannelaccount'
import * as deliverChannelUserApi from '@/api/deliver/deliverchanneluser'

import * as categoryApi from '@/api/page/category'
import * as templateApi from '@/api/page/template'
import * as pageApi from '@/api/page/page'
import * as pageRedirectApi from '@/api/page/redirect'
import * as pageCollectRecordApi from '@/api/page/pagecollectrecord'
import * as pageHostDateApi from '@/api/page/pagehostdate'
import * as pageIdentifierBlackListApi from '@/api/page/pageidentifierblacklist'
import * as pageIPBlackListApi from '@/api/page/pageipblacklist'
import * as pageIPDateApi from '@/api/page/pageipdate'

import * as domainApi from '@/api/domain/domain'
import * as domainBlackListApi from '@/api/domain/domainblacklist'

import * as actionConfigApi from '@/api/action/actionconfig'

export {
  loginApi,
  menuApi,
  roleApi,
  sysUserApi,
  authApi,
  redisApi,
  systemNoticeApi,
  userdatapermissionApi,
  fileApi,
  optionsApi,
  fBUserApi,
  fBBusinessesApi,
  fBAdAccountApi,
  fBAdApi,
  fBAdSetApi,
  fBAdCampaignApi,
  fBadinsightsApi,
  counterOrderConfigApi,
  counterOrderDateRecordsApi,
  deliverDashboardApi,
  deliverChannelApi,
  deliverTaskApi,
  deliverAreaApi,
  deliverStatsApi,
  deliverTaskInFansSpeedApi,
  deliverTaskChangeFansApi,
  deliverTaskChannelAccountApi,
  deliverChannelUserApi,
  categoryApi,
  templateApi,
  pageApi,
  pageRedirectApi,
  pageCollectRecordApi,
  pageHostDateApi,
  pageIdentifierBlackListApi,
  pageIPBlackListApi,
  pageIPDateApi,
  domainApi,
  domainBlackListApi,
  actionConfigApi,
}
