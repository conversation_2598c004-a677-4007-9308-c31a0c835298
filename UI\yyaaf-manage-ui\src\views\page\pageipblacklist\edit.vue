<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="IP" prop="ip">
            <el-input v-model="form.ip" placeholder="请输入IP" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { pageIPBlackListApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      isEdit: false,
      form: {},
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
      this.isEdit = false
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.isEdit = true
      this.$xloading.show()
      const res = await pageIPBlackListApi.getDetail(row.ip)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          if (this.isEdit) {
            const res = await pageIPBlackListApi.editPageIPBlackList(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await pageIPBlackListApi.addPageIPBlackList(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
