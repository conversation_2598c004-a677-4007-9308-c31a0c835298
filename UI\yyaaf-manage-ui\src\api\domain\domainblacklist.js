import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/domainBlackList/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/domainBlackList/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addDomainBlackList(data) {
  return request({
    url: '/domainBlackList/add',
    method: 'post',
    data: data,
  })
}

export function editDomainBlackList(data) {
  return request({
    url: '/domainBlackList/edit',
    method: 'post',
    data: data,
  })
}

export function delDomainBlackList(id) {
  return request({
    url: '/domainBlackList/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------DomainBlackList结束----------
