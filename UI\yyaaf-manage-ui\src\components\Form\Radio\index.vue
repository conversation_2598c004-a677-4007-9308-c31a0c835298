<template>
  <el-radio-group v-bind="$attrs" v-on="$listeners" :value="currentValue" @input="handleInput">
    <slot v-bind:options="optionData">
      <template v-if="button">
        <el-radio-button v-for="item in optionData" :key="item.value" :label="item.value">{{
          item.label
        }}</el-radio-button>
      </template>
      <template v-else>
        <el-radio v-for="item in optionData" :key="item.value" :label="item.value">{{
          item.label
        }}</el-radio>
      </template>
    </slot>
  </el-radio-group>
</template>

<script>
import request from '@/utils/request'

export default {
  inheritAttrs: false,
  props: {
    showDefault: {
      // 是否显示默认的选项（全部）
      type: Boolean,
      default: false,
    },
    options: Array, // 选项的数据，对象的属性为 { label: "xxx", value: xxx }
    url: String, // url，对象属性同上
    value: {
      type: [String, Number, Boolean],
    },
    button: {
      type: <PERSON>olean,
      default: false,
    },
    map: Function, // 请求返回的参数不是name/label格式时，传入方法进行处理
  },
  data() {
    return {
      optionData: [],
      currentValue: this.value,
    }
  },
  mounted() {
    // console.log(this.url)
    if (this.options) {
      this.updateOptionsData(this.options)
    }
    if (this.url) {
      this.loadOptions(this.url)
    }
  },
  watch: {
    options(newVal) {
      this.updateOptionsData(newVal)
    },
    url(newVal) {
      // console.log(newVal)
      this.loadOptions(this.url)
    },
    value(newVal) {
      // console.log(newVal)
      this.setCurrentValue(newVal)
    },
  },
  methods: {
    async loadOptions(url) {
      const res = await request({
        url: url,
        method: 'get',
      })
      if (res.code == 0) {
        if (this.map) {
          // 处理返回的参数
          this.updateOptionsData(this.map(res.data))
        } else {
          this.updateOptionsData(res.data)
        }
      } else {
        console.error(`radio组件获取数据失败【${url}】：${res.msg}`)
      }
    },
    handleInput(value) {
      // console.log('handleInput:', value)
      this.setCurrentValue(value)
      this.$emit('input', value)
    },
    setCurrentValue(value) {
      if (value == this.currentValue) {
        return
      }
      this.currentValue = value
    },
    updateOptionsData(options) {
      if (this.showDefault) {
        var defaultOptionItem = { label: '全部', value: -1 }
        this.optionData = [defaultOptionItem, ...options]
        return
      }
      this.optionData = options
    },
  },
}
</script>

<style></style>
