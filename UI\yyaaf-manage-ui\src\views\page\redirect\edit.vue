<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="id" prop="id">
            <el-input :disabled="true" v-model="form.id" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地区" prop="areaId">
            <x-select
              v-model="form.areaId"
              url="/deliverArea/options"
              style="width: 100%"
            ></x-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="问候文字" prop="wsText">
            <el-input
              type="textarea"
              :autosize="{ minRows: 3 }"
              v-model="form.wsText"
              placeholder="请输入问候携带的文字"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="渠道标识" prop="cId">
            <x-select filterable v-model="form.cId" url="/deliverChannel/options"></x-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="像素" prop="pixelId">
            <el-input v-model="form.pixelId" placeholder="请输入像素Id" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用" prop="enable">
            <x-radio v-model="form.enable" button :options="enableOptions" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { enableOptions } from '@/utils/options'
import { pageRedirectApi } from '@/api'
import { randomString } from '@/utils'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {
        title: randomString(32),
        enable: true,
      },
      templateOptions: [],
      enableOptions,
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await pageRedirectApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {
        title: randomString(32),
        enable: true,
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await pageRedirectApi.editPageRedirect(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await pageRedirectApi.addPageRedirect(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
