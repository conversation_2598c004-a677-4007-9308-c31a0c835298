<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="类别">
          <x-select show-default v-model="queryParams.cId" url="/category/options"></x-select>
        </el-form-item>

        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['template:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="name" label="模板名称/标识" align="center" min-width="200">
          <template slot-scope="scope">
            <el-link type="primary" :underline="false" @click="handlePreview(scope.row)">
              <div>{{ scope.row.name }}</div>
            </el-link>
            <br />
            <div>{{ scope.row.tempId }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="cName"
          label="模板类别"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="createOn"
          label="创建时间"
          align="center"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="editTime"
          label="编辑时间"
          align="center"
          min-width="120"
        ></el-table-column>
        <el-table-column label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.enable">启用</el-tag>
            <el-tag type="danger" size="medium" v-else>禁用</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="230" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handlePreview(scope.row)"
              v-permission="['template:preview']"
              >预览</el-button
            >
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['template:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['template:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { templateApi } from '@/api'
import EditDialog from './edit'
export default {
  components: {
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await templateApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      //this.$refs.editDialog.add()
      this.$router.push({ path: '/page/templateadd' })
    },
    handleEdit(row) {
      //this.$refs.editDialog.edit(row)
      this.$router.push({ path: '/page/templateadd', query: { id: row.id } })
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await templateApi.delTemplate(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    async handlePreview(row) {
      this.$xloading.show()
      const res = await templateApi.preview(row.id)
      const htmlContent = res
      const win = window.open()
      win.document.open()
      win.document.write(htmlContent)
      win.document.close()
      this.$xloading.hide()
    },
    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
