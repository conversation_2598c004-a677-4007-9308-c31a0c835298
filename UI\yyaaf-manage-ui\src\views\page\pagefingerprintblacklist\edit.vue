<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="指纹ID" prop="fingerprintId">
            <el-input v-model="form.fingerprintId" placeholder="请输入点击追踪指纹ID" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="禁用状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择禁用状态">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { pageFingerprintBlackListApi } from '@/api'
import { getPageCollectRecordStatusOptions } from '@/utils/options'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      isEdit: false,
      form: {
        status: 0,
      },
      statusOptions: [],
    }
  },
  async created() {
    this.statusOptions = await getPageCollectRecordStatusOptions()
  },
  methods: {
    add(fingerprintId) {
      this.reset()
      this.title = '新增'
      this.visible = true
      this.isEdit = false
      if (fingerprintId) this.form.fingerprintId = fingerprintId
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.isEdit = true
      this.$xloading.show()
      const res = await pageFingerprintBlackListApi.getDetail(row.fingerprintId)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.form.status = 0
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          if (this.isEdit) {
            const res = await pageFingerprintBlackListApi.editPageFingerprintBlackList(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await pageFingerprintBlackListApi.addPageFingerprintBlackList(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
