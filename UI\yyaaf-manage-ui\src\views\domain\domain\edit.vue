<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="12">
          <el-form-item label="id" prop="id">
            <el-input :disabled="true" v-model="form.id" placeholder="Id自动生成" />
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="域名" prop="domain">
            <el-input v-model="form.domain" placeholder="请输入域名" />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="权重" prop="weight">
            <el-input-number v-model="form.weight" placeholder="请输入权重" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="state">
            <x-radio v-model="form.state" button :options="stateOptions" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="随机二级" prop="isRandomSLD">
            <x-radio v-model="form.isRandomSLD" button :options="randomSLDOptions" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { domainApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {
        weight: 10,
        state: 1,
      },
      stateOptions: [
        { label: '上线', value: 1 },
        { label: '下线', value: 0 },
        { label: '禁用', value: -1 },
      ],
      randomSLDOptions: [
        { label: '随机', value: true },
        { label: '固定', value: false },
      ],
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await domainApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {
        weight: 10,
        state: 1,
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await domainApi.editDomain(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await domainApi.addDomain(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
