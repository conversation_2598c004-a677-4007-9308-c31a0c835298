import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/pageIdentifierBlackList/getList',
    method: 'get',
    params,
  })
}

export function getDetail(identifier) {
  return request({
    url: '/pageIdentifierBlackList/getDetail',
    method: 'get',
    params: {
      identifier,
    },
  })
}

export function addPageIdentifierBlackList(data) {
  return request({
    url: '/pageIdentifierBlackList/add',
    method: 'post',
    data: data,
  })
}

export function editPageIdentifierBlackList(data) {
  return request({
    url: '/pageIdentifierBlackList/edit',
    method: 'post',
    data: data,
  })
}

export function delPageIdentifierBlackList(identifier) {
  return request({
    url: '/pageIdentifierBlackList/delete',
    method: 'post',
    data: {
      identifier,
    },
  })
}

//----------PageIdentifierBlackList结束----------
