<template>
  <el-checkbox-group v-bind="$attrs" v-on="$listeners" :value="currentValue" @input="handleInput">
    <slot v-bind:options="optionData">
      <el-checkbox v-for="item in optionData" :key="item.value" :label="item.value">{{
        item.label
      }}</el-checkbox>
    </slot>
  </el-checkbox-group>
</template>

<script>
import request from '@/utils/request'

export default {
  inheritAttrs: false,
  props: {
    options: Array, // 选项的数据，对象的属性为 { name: "xxx", label: xxx }
    url: String, // url，对象属性同上
    value: {
      type: Array,
    },
    map: Function, // 请求返回的参数不是name/label格式时，传入方法进行处理
  },
  data() {
    return {
      optionData: [],
      currentValue: this.value,
    }
  },
  mounted() {
    // console.log(this.url)
    if (this.options) {
      this.optionData = this.options
    }
    if (this.url) {
      this.loadOptions(this.url)
    }
  },
  watch: {
    options(newVal) {
      this.optionData = newVal
    },
    url(newVal) {
      // console.log(newVal)
      this.loadOptions(this.url)
    },
    value(newVal) {
      // console.log(newVal)
      this.setCurrentValue(newVal)
    },
  },
  methods: {
    async loadOptions(url) {
      const res = await request({
        url: url,
        method: 'get',
      })
      if (res.code == 0) {
        if (this.map) {
          // 处理返回的参数
          this.optionData = this.map(res.data)
        } else {
          this.optionData = res.data
        }
      } else {
        console.error(`checkbox组件获取数据失败【${url}】：${res.msg}`)
      }
    },
    handleInput(value) {
      // console.log('handleInput:', value)
      this.setCurrentValue(value)
      this.$emit('input', value)
    },
    setCurrentValue(value) {
      if (value == this.currentValue) {
        return
      }
      this.currentValue = value
    },
  },
}
</script>

<style></style>
