import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/fBAdAccount/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/fBAdAccount/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addFBAdAccount(data) {
  return request({
    url: '/fBAdAccount/add',
    method: 'post',
    data: data,
  })
}

export function editFBAdAccount(data) {
  return request({
    url: '/fBAdAccount/edit',
    method: 'post',
    data: data,
  })
}

export function delFBAdAccount(id) {
  return request({
    url: '/fBAdAccount/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------FBAdAccount结束----------
