<template>
    <div class="app-container">
      <el-card>
        <el-form ref="queryForm" label-width="80px" inline size="mini">
          <el-form-item label="所属用户">
            <x-select
              show-default
              v-model="queryParams.fbUserId"
              url="/fBUser/options"
            ></x-select>
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" icon="el-icon-search"  @click="$refs.table.refresh(true)">搜索</el-button>
            <el-button size="mini" icon="el-icon-refresh"  @click="queryReset">重置</el-button>
          </el-form-item>
        </el-form>
  
        <!--<el-row ref="toolbar" class="table-toolbar">
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd" v-permission="['fBBusinesses:add']">新增</el-button>
        </el-row> -->
        
        <!-- 表格 -->
        <x-table
          ref="table"
          v-loading="loading"
          :data="tableData"
          row-key="id"
          :loadData="getList"
          :height="tableHeight"
          >
          <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
          <el-table-column prop="id" label="业务Id" align="center" min-width="100"></el-table-column>
          <el-table-column prop="name" label="业务名称" align="center" min-width="200"></el-table-column>
          <el-table-column prop="createOn" label="抓取时间" align="center" min-width="150"></el-table-column>
   
          <!--<el-table-column label="操作" width="160" align="center">
            <template slot-scope="scope">
              <el-button size="mini" 
                type="warning"
                @click="handleEdit(scope.row)"
                v-permission="['fBBusinesses:edit']"
              >修改</el-button>
              <el-button size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
                v-permission="['fBBusinesses:delete']"
              >删除</el-button>
            </template>
          </el-table-column>-->
        </x-table>
      </el-card>
  
      <edit-dialog ref="editDialog" @ok="handleOk"/>
    </div>
  </template>
  
  <script>
  import { tableHeightMixin } from '@/mixin'
  import { fBBusinessesApi } from '@/api'
  export default {
    components: {},
    mixins:[tableHeightMixin],
    data () {
      return {
        queryParams: {
          fbUserId: -1,
        },
        loading: false,
        tableData: {},
      }
    },
    methods: {
      queryReset () {
        this.queryParams = {
          fbUserId: -1,
        }
        this.$refs.table.refresh(true)
      },
      async getList(params) {
        this.$xloading.show()
        params = Object.assign({}, params, this.queryParams)
        const res = await fBBusinessesApi.getList(params)
        this.tableData = res
        this.$xloading.hide()
      },
      handleAdd () {
        this.$refs.editDialog.add()
      },
      handleEdit (row) {
        this.$refs.editDialog.edit(row)
      },
      async handleDelete (row) {
        this.$confirm('是否确认删除该数据项？', '提示', {
          type: 'warning'
        }).then(async () => {
          this.$xloading.show()
          const res = await fBBusinessesApi.delFBBusinesses(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        }).catch(() => {
          this.$xloading.hide()
        })
        
      },
  
      handleOk () {
        this.$refs.table.refresh()
      },
    }
  }
  </script>
  