<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="visible = false"
    width="700px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <!-- 当前信息显示 -->
      <el-card class="current-info-card" shadow="never">
        <div slot="header" class="card-header">
          <span>当前账号信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">当前主号:</span>
              <el-tag type="primary" size="medium">{{ currentAccount }}</el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">渠道:</span>
              <el-tag type="success" size="medium">{{ currentChannel }}</el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 新主号输入 -->
      <el-form-item label="新主号" prop="account" style="margin-top: 20px">
        <el-input v-model="form.account" placeholder="请输入新的主号" clearable show-word-limit>
          <template slot="prepend">
            <i class="el-icon-user"></i>
          </template>
        </el-input>
        <div class="form-tip">
          <i class="el-icon-info"></i>
          请输入要替换的新主号账号
        </div>
      </el-form-item>
      <el-form-item label="换号类型" prop="changeAccountType">
        <x-radio v-model="form.changeAccountType" :options="changeAccountOptions" />
        <div class="form-tip">
          <i class="el-icon-info"></i>
          新号继承旧号进线数: 当新号的计数器是从旧号的进线数开始计数是选择这个。
        </div>
        <div class="form-tip">
          <i class="el-icon-info"></i>
          新号不继承旧号进线数: 当新号的计数器是从0开始计数是选择这个。
        </div>
      </el-form-item>
    </el-form>
  </x-dialog>
</template>

<script>
import { deliverTaskChannelAccountApi } from '@/api'

export default {
  data() {
    return {
      title: '换号操作',
      visible: false,
      loading: false,
      currentRow: null,
      form: {
        id: null,
        account: '',
        changeAccountType: 1,
      },
      changeAccountOptions: [
        { label: '新号继承旧号进线数', value: 1 },
        { label: '新号不继承旧号进线数', value: 2 },
      ],
      rules: {
        account: [
          { required: true, message: '请输入新主号', trigger: 'blur' },
          //{ min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        ],
      },
    }
  },

  computed: {
    currentAccount() {
      return this.currentRow ? this.currentRow.account : ''
    },
    currentChannel() {
      if (!this.currentRow) return ''
      return this.currentRow.cName
        ? `${this.currentRow.cName} (${this.currentRow.cId})`
        : this.currentRow.cId
    },
  },

  methods: {
    open(row) {
      this.currentRow = row
      this.form.id = row.id
      this.title = `换号操作 - ${row.account}`
      this.visible = true
      this.resetForm()
    },

    resetForm() {
      this.form = {
        id: this.currentRow ? this.currentRow.id : null,
        account: '',
        changeAccountType: 1,
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },

    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          await this.handleChangeAccount()
        }
      })
    },

    async handleChangeAccount() {
      this.$confirm(
        `确认将主号 "${this.currentAccount}" 换为 "${this.form.account}" 吗？`,
        '换号确认',
        {
          type: 'warning',
          confirmButtonText: '确认换号',
          cancelButtonText: '取消',
        }
      )
        .then(async () => {
          try {
            this.loading = true

            const data = {
              id: this.form.id,
              account: this.form.account,
              changeAccountType: this.form.changeAccountType,
            }

            const res = await deliverTaskChannelAccountApi.change(data)

            if (res.code === 0) {
              this.$xMsgSuccess('换号成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('换号失败：' + res.msg)
            }
          } catch (error) {
            this.$xMsgError('换号失败：' + error.message)
          } finally {
            this.loading = false
          }
        })
        .catch(() => {
          // 用户取消操作
        })
    },
  },
}
</script>

<style scoped>
.current-info-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.card-header {
  font-weight: 600;
  color: #303133;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 70px;
}

.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
}

.form-tip i {
  margin-right: 4px;
}

/* 输入框样式增强 */
.el-input-group__prepend {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #909399;
}

/* 卡片内容样式 */
.current-info-card .el-card__body {
  padding: 16px;
}

/* 标签样式调整 */
.el-tag {
  font-weight: 500;
}

/* 表单项间距调整 */
.el-form-item {
  margin-bottom: 22px;
}

/* 对话框内容区域 */
.el-dialog__body {
  padding: 20px 20px 10px 20px;
}
</style>
