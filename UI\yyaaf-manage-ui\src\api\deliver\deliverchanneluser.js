import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/deliverChannelUser/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/deliverChannelUser/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addchannelUser(data) {
  return request({
    url: '/deliverChannelUser/add',
    method: 'post',
    data: data,
  })
}

export function editchannelUser(data) {
  return request({
    url: '/deliverChannelUser/edit',
    method: 'post',
    data: data,
  })
}

export function delchannelUser(id) {
  return request({
    url: '/deliverChannelUser/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function resetPassword(id) {
  return request({
    url: '/deliverChannelUser/resetPassword',
    method: 'post',
    data: {
      id,
    },
  })
}

export function customerLogin(userName) {
  return request({
    url: '/deliverChannelUser/customerLogin',
    method: 'post',
    data: {
      userName,
    },
  })
}
