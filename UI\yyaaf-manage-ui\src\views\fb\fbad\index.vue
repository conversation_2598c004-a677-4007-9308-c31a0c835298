<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="广告ID">
          <el-input v-model="queryParams.id"></el-input>
        </el-form-item>
        <el-form-item label="广告账户ID">
          <el-input v-model="queryParams.account_id"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          @click="handleActiveAd"
          v-permission="['fBAd:changestatus']"
          >开启广告</el-button
        >

        <el-button
          size="mini"
          type="warning"
          @click="handlePausedAd"
          v-permission="['fBAd:changestatus']"
          >暂停广告</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
        @selection-change="handleSelectionChange"
        :cell-style="{ fontSize: '13px' }"
      >
        <el-table-column type="selection" align="center" width="50"></el-table-column>
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column label="广告名称/ID" align="center" min-width="200">
          <template v-slot="{ row }">
            <two-row-column :firstText="row.name" :secondText="row.id" />
          </template>
        </el-table-column>
        <el-table-column label="广告组名称/ID" align="center" min-width="200">
          <template v-slot="{ row }">
            <two-row-column :firstText="row.adset_name" :secondText="row.adset_id" />
          </template>
        </el-table-column>
        <el-table-column label="广告系列名称/ID" align="center" min-width="200">
          <template v-slot="{ row }">
            <two-row-column :firstText="row.campaign_name" :secondText="row.campaign_id" />
          </template>
        </el-table-column>
        <el-table-column label="账户名称/ID" align="center" min-width="200">
          <template v-slot="{ row }">
            <two-row-column :firstText="row.account_name" :secondText="row.account_id" />
          </template>
        </el-table-column>
        <el-table-column
          prop="bid_amount_cal"
          label="出价金额"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="effective_status"
          label="生产状态"
          align="center"
          min-width="130"
        ></el-table-column>
        <el-table-column label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.status == 'ACTIVE'">启用</el-tag>
            <el-tag type="warning" size="medium" v-else-if="row.status == 'PAUSED'">暂停</el-tag>
            <el-tag type="danger" size="medium" v-else-if="row.status == 'DELETED'">删除</el-tag>
            <el-tag type="danger" size="medium" v-else>归档</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间/更新时间" align="center" min-width="160">
          <template v-slot="{ row }">
            <span>{{ row.created_time }}</span>
            <br />
            <span>{{ row.updated_time }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="lastUpdateTime"
          label="最后抓取时间"
          align="center"
          min-width="160"
        ></el-table-column>

        <!--<el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['fBAd:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['fBAd:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>-->
      </x-table>
    </el-card>

    <!--<edit-dialog ref="editDialog" @ok="handleOk" />-->
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { fBAdApi } from '@/api'
//import EditDialog from './edit'
import TwoRowColumn from '@/components/Table/CustomColumn/TwoRowColumn'

export default {
  components: {
    //EditDialog,
    TwoRowColumn,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      selections: [],
      queryParams: {},
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await fBAdApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await fBAdApi.delFBAd(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },

    handleSelectionChange(selection) {
      this.selections = selection.map((item) => ({ id: item.id, account_id: item.account_id }))
    },

    handleActiveAd() {
      if (this.selections.length > 0) {
        this.$confirm('是否确认开启广告？', '提示', {
          type: 'warning',
        })
          .then(async () => {
            this.selections.forEach(async (item) => {
              this.$xloading.show()
              const params = {
                id: item.id,
                account_id: item.account_id,
                status: 'ACTIVE',
              }
              const res = await fBAdApi.changeStatus(params)
              this.$xloading.hide()
              if (res.code == 0) {
                this.$xMsgSuccess('操作成功')
              } else {
                this.$xMsgError('操作失败！' + res.msg)
              }
            })
            this.$refs.table.refresh()
          })
          .catch(() => {
            this.$xloading.hide()
          })
      } else {
        this.$xMsgWarning('请选择要操作的广告！')
      }
    },

    handlePausedAd() {
      if (this.selections.length > 0) {
        this.$confirm('是否确认暂停广告？', '提示', {
          type: 'warning',
        })
          .then(async () => {
            this.selections.forEach(async (item) => {
              this.$xloading.show()
              const params = {
                id: item.id,
                account_id: item.account_id,
                status: 'PAUSED',
              }
              const res = await fBAdApi.changeStatus(params)
              this.$xloading.hide()
              if (res.code == 0) {
                this.$xMsgSuccess('操作成功')
              } else {
                this.$xMsgError('操作失败！' + res.msg)
              }
            })
            this.$refs.table.refresh()
          })
          .catch(() => {
            this.$xloading.hide()
          })
      } else {
        this.$xMsgWarning('请选择要操作的广告！')
      }
    },
  },
}
</script>
