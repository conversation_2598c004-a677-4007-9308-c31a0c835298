import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/counterOrderConfig/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/counterOrderConfig/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addCounterOrderConfig(data) {
  return request({
    url: '/counterOrderConfig/add',
    method: 'post',
    data: data,
  })
}

export function batchCounterOrderConfig(data) {
    return request({
      url: '/counterOrderConfig/batch',
      method: 'post',
      data: data,
    })
  }
  

export function editCounterOrderConfig(data) {
  return request({
    url: '/counterOrderConfig/edit',
    method: 'post',
    data: data,
  })
}

export function delCounterOrderConfig(id) {
  return request({
    url: '/counterOrderConfig/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------CounterOrderConfig结束----------
