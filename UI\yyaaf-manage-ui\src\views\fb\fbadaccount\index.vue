<template>
  <div class="app-container">
    <el-card>
      <el-form ref="queryForm" label-width="80px" inline size="mini">
        <el-form-item>
          <el-form-item label="所属用户">
            <x-select show-default v-model="queryParams.fbUserId" url="/fBUser/options"></x-select>
          </el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button size="mini" type="primary" icon="el-icon-user" @click="handleSyncUserData"
          >同步账户</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <!--<el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>-->
        <el-table-column label="状态" align="center" min-width="100" fixed="left">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.disableReason == 0">正常</el-tag>
            <el-tag type="danger" size="medium" v-else>禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="账号" align="center" min-width="200" fixed="left">
          <template v-slot="{ row }">
            <span>
              <el-link type="primary" @click="handleFBAdManager(row)" target="_blank">
                {{ row.name }}
              </el-link>
            </span>
            <br />
            <span>{{ row.accountId }}</span>
            <br />
            <el-link type="primary" @click="handleStats(row)" target="_blank"> 每日数据 </el-link>
          </template>
        </el-table-column>
        <el-table-column label="管理员" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ row.adminUserCount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="账号类型" align="center" min-width="180">
          <template v-slot="{ row }">
            <span v-if="row.fbBusinessId">Business</span>
            <span v-else>Personal normal</span>
          </template>
        </el-table-column>
        <el-table-column label="账单金额" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ (row.balance / 1).toFixed(2) }} {{ row.currency }}</span>
            <!--<br />
            <el-tag size="medium">{{ (row.balance / 1).toFixed(2) }} USD</el-tag>-->
          </template>
        </el-table-column>
        <el-table-column label="门槛" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ (row.thresholdAmount / 100).toFixed(2) }} {{ row.currency }}</span>
            <!--<br />
            <el-tag size="medium">{{ (row.thresholdAmount / 100).toFixed(2) }} USD</el-tag>-->
          </template>
        </el-table-column>
        <el-table-column label="日限额" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ row.adtrustDsl.toFixed(2) }} {{ row.currency }}</span>
            <!--<br />
            <el-tag size="medium">{{ row.adtrustDsl.toFixed(2) }} USD</el-tag>-->
          </template>
        </el-table-column>
        <el-table-column label="总花费" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ (row.amountSpent / 100).toFixed(2) }} {{ row.currency }}</span>
            <!--<br />
            <el-tag size="medium">{{ (row.amountSpent / 100).toFixed(2) }} USD</el-tag>-->
          </template>
        </el-table-column>
        <el-table-column label="花费限额" align="center" min-width="150">
          <template v-slot="{ row }">
            <div v-if="row.spendCap == 0">
              <span>-</span>
            </div>
            <div v-else>
              <span>{{ (row.spendCap / 100).toFixed(2) }} {{ row.currency }}</span>
              <!--<br />
              <el-tag size="medium">{{ (row.spendCap / 100).toFixed(2) }} USD</el-tag>-->
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="currency"
          label="币种"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column label="账户类型" align="center" min-width="150">
          <template v-slot="{ row }">
            <span v-if="row.isPrepayAccount">预付费</span>
            <span v-else>后付费</span>
          </template>
        </el-table-column>
        <el-table-column label="所有者角色" align="center" min-width="150">
          <template v-slot="{ row }">
            <el-tag size="medium" v-if="row.userRole == 'Admin'">{{ row.userRole }}</el-tag>
            <el-tag type="info" size="medium" v-else>{{ row.userRole }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="支付方法" align="center" min-width="200">
          <template v-slot="{ row }">
            <span v-html="allPaymentFormat(row.allPaymentMethodsPmCreditCard)"></span>
          </template>
        </el-table-column>
        <el-table-column label="账单期" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ row.nextBillDateStr }}</span>
          </template>
        </el-table-column>
        <el-table-column label="锁定原因" align="center" min-width="300">
          <template v-slot="{ row }">
            <div v-if="row.disableReason == 0">
              <span>-</span>
            </div>
            <div v-else>
              <el-tag type="danger" size="medium">{{ row.disableReasonStr }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="创建日期"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column label="时区" align="center" min-width="300">
          <template v-slot="{ row }">
            <span>{{ row.timezoneName }} | {{ row.timezoneOffsetHoursUtc }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="fbBusinessId"
          label="创建自BM"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="businessCountryCode"
          label="国家编码"
          align="center"
          min-width="150"
        ></el-table-column>
        <!--<el-table-column label="操作" width="160" align="center">
            <template v-slot="scope">
              <el-button size="mini" 
                type="warning"
                @click="handleEdit(scope.row)"
                v-permission="['fBAdAccount:edit']"
              >修改</el-button>
              <el-button size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
                v-permission="['fBAdAccount:delete']"
              >删除</el-button>
            </template>
          </el-table-column>-->
      </x-table>
    </el-card>
  </div>
</template>

<script>
import * as message from '@/utils/message'
import { Notification } from 'element-ui'
import { tableHeightMixin } from '@/mixin'
import { fBAdAccountApi } from '@/api'
export default {
  components: {},
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        fbUserId: -1,
      },
      loading: false,
      tableData: {},
      syncLock: false,
    }
  },
  mounted() {
    this.listenerMessage()
  },
  beforeDestroy() {
    window.removeEventListener('message', this.handleMessage)
  },
  methods: {
    queryReset() {
      this.queryParams = {
        fbUserId: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await fBAdAccountApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await fBAdAccountApi.delFBAdAccount(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },

    allPaymentFormat(cardstr) {
      var cards = cardstr.split('|')
      var newCradstr = ''
      cards.forEach((element) => {
        newCradstr += element + '<br/>'
      })
      newCradstr = newCradstr.slice(0, -5)
      return newCradstr
    },

    handleFBAdManager(row) {
      var url = `https://adsmanager.facebook.com/adsmanager/manage/campaigns?act=${row.accountId}`
      window.open(url)
    },

    handleStats(row) {
      this.$router.push({ path: '/stats/adinsights', query: { account_id: row.accountId } })
    },
    handleSyncUserData() {
      if (!this.syncLock) {
        this.syncLock = true
        this.$xMsgInfo('账户开始同步，请等候同步通知！')
        message.postMessage({ type: 'SyncUserData' })
      } else {
        this.$xMsgWarning('账户正在同步中，请不要重复操作！')
      }
    },
    listenerMessage() {
      window.addEventListener(
        'message',
        (event) => {
          // 确保数据来源安全 (建议替换 '*' 为特定的源)
          //console.log('Received message:', event.data)
          if (event.data.type === 'SyncUserDataResponse') {
            console.log('收到 Content Script 发送回来的数据：', event.data.data)
            this.syncLock = false
            var res = event.data.data
            if (res.code == 1) {
              Notification({
                title: '账户同步成功',
                message: '账户同步成功',
                type: 'success',
                duration: 0, // 不自动关闭
                position: 'bottom-right',
                dangerouslyUseHTMLString: true,
              })
            }
            return
          }
        },
        false
      )
    },
  },
}
</script>
