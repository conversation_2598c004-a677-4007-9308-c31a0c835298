<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="visible = false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="medium">
      <el-row>
        <el-col :span="16">
          <el-form-item label="加号渠道标识" prop="cid">
            <el-autocomplete
              class="inline-input"
              :fetch-suggestions="querySearch"
              placeholder="请输入加号的目标渠道标识"
              :trigger-on-focus="false"
              v-model="form.cid"
              style="width: 100%"
              :debounce="50"
              @select="handleSelect"
              prefix-icon="el-icon-search"
            ></el-autocomplete>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主号列表" prop="accounts">
            <el-input
              type="textarea"
              :autosize="{ minRows: 8 }"
              v-model="formattedAccounts"
              placeholder="请输入主号，多个换行分隔"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { getChannelOptions } from '@/utils/options'
import { deliverTaskChannelAccountApi } from '@/api'

export default {
  components: {},
  data() {
    return {
      ids: [],
      title: '',
      visible: false,
      loading: false,
      accounts: [],
      form: {
        accounts: [],
      },
      channelOptions: [],
    }
  },

  async created() {
    this.channelOptions = await getChannelOptions()
  },
  computed: {
    formattedAccounts: {
      get() {
        // 将数组转换为换行分隔的字符串
        return this.accounts.join('\n')
      },
      set(value) {
        // 将输入的换行符分隔的字符串转回数组
        this.accounts = value.split('\n')
      },
    },
  },
  methods: {
    async add(taskid) {
      console.log('taskid: ', taskid)
      this.reset()
      this.title = '加号操作'
      this.visible = true
      this.form.taskid = taskid
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          const data = Object.assign({}, this.form, {
            accounts: this.accounts,
          })
          await this.handleAdd(data)
        }
      })
    },

    async handleAdd(data) {
      this.$confirm(`是否确认进行加号操作？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()

          // 循环处理每个账号
          const accounts = data.accounts || []
          let successCount = 0
          let failCount = 0
          const failedAccounts = []

          for (let i = 0; i < accounts.length; i++) {
            const account = accounts[i]
            if (!account || account.trim() === '') continue // 跳过空账号

            try {
              // 为每个账号创建单独的数据对象
              const singleAccountData = {
                ...data,
                account: account.trim(),
              }

              const res = await deliverTaskChannelAccountApi.add(singleAccountData)

              if (res.code == 0) {
                successCount++
              } else {
                failCount++
                failedAccounts.push(`${account}: ${res.msg}`)
                this.$xMsgError(res.msg)
              }
            } catch (error) {
              failCount++
              failedAccounts.push(`${account}: ${error.message || '网络错误'}`)
            }
          }

          this.$xloading.hide()

          // 显示处理结果
          if (failCount === 0) {
            this.$xMsgSuccess(`操作成功！共处理 ${successCount} 个账号`)
            this.visible = false
            this.$emit('ok')
          } else {
            this.$emit('ok')
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    querySearch(queryString, cb) {
      //console.log(queryString)
      let restaurants = this.channelOptions
      let results = queryString
        ? restaurants.filter(
            (e) =>
              e.value.toLowerCase().indexOf(queryString.toLowerCase()) > -1 ||
              e.label.toLowerCase().indexOf(queryString.toLowerCase()) > -1
          )
        : restaurants

      results = results.map((o) => {
        return {
          value: o.label + ' 【' + o.value + '】',
          name: o.value,
        }
      })

      // 调用 callback 返回建议列表的数据
      cb(results)
    },

    handleSelect(item) {
      console.log(item)
      this.form.cid = item.name
    },
  },
}
</script>

<style></style>
