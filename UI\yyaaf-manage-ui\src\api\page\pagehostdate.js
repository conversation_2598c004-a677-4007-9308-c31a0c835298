import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/pageHostDate/getList',
    method: 'get',
    params,
  })
}

export function addToBlackList(data) {
  return request({
    url: '/pageHostDate/addToBlackList',
    method: 'post',
    data,
  })
}

export function removeBlackList(data) {
  return request({
    url: '/pageHostDate/removeBlackList',
    method: 'post',
    data,
  })
}
