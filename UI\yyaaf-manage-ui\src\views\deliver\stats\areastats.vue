<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="地区">
          <x-select show-default v-model="queryParams.areaId" url="/deliverArea/options"></x-select>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="handleStats"
          >重新统计</el-button
        >
      </el-row>
      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :show-summary="true"
        :summary-method="getSummaries"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :row-class-name="highlightChildren"
        @expand-change="handleExpandChange"
      >
        >
        <el-table-column label="日期" align="center" min-width="130">
          <template v-slot="{ row }">
            <span>{{ row.date.slice(0, 10) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="areaName"
          label="地区"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="totalTarget"
          label="目标"
          sortable
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="trueNewFans"
          label="进线"
          sortable
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="changeNewFans"
          label="调粉"
          sortable
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="bannedNewFans"
          label="封号"
          sortable
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="residue"
          label="剩余(容量)"
          sortable
          align="center"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="eequally"
          label="公摊数"
          sortable
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="totalFans"
          label="总粉"
          sortable
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="trueRepeatFans"
          label="重复"
          sortable
          align="center"
          min-width="100"
        ></el-table-column>
        <!--<el-table-column label="利润" align="center" sortable min-width="100">
            <template v-slot="{ row }">
                <span>{{row.profit}} 元</span>
            </template>
          </el-table-column>-->
        <el-table-column label="重复率" align="center" sortable min-width="100">
          <template v-slot="{ row }">
            <span>{{ row.repeatFansPro }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="单价" align="center" sortablemin-width="100">
          <template v-slot="{ row }">
            <span>{{ row.price }} 元</span>
          </template>
        </el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { deliverStatsApi, deliverTaskApi } from '@/api'
//import EditDialog from './Edit'
export default {
  components: {
    //EditDialog
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        date: new Date().toISOString().slice(0, 10),
      },
      loading: false,
      tableData: {},
      expandedChildren: new Set(), // 存储展开的 children 行 ID
    }
  },
  created() {},
  methods: {
    queryReset() {
      this.queryParams = {
        date: new Date().toISOString().slice(0, 10),
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await deliverStatsApi.getAreaStatsList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map((item) => Number(item[column.property]))
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => prev + curr, 0)
        } else {
          sums[index] = '--'
        }
      })
      return sums
    },
    // 处理展开事件
    handleExpandChange(row, expanded) {
      //console.log('当前行:', row, '展开状态:', expanded)
      if (expanded) {
        // 展开时，添加子节点 ID
        if (row.children && row.children.length > 0) {
          row.children.forEach((child) => this.expandedChildren.add(child.id))
        }
      } else {
        // 收起时，移除子节点 ID
        if (row.children && row.children.length > 0) {
          row.children.forEach((child) => this.expandedChildren.delete(child.id))
        }
      }
    },

    // 动态行样式
    highlightChildren({ row }) {
      return this.expandedChildren.has(row.id) ? 'highlight-row' : ''
    },

    async handleStats() {
      const date = this.queryParams.date
      const res = await deliverTaskApi.stats(date)
      if (res.code === 0) {
        this.$xMsgSuccess('重新统计成功')
      } else {
        this.$xMsgError(res.msg)
      }
    },
  },
}
</script>

<style>
/* 高亮展开的子行 */
.highlight-row {
  background-color: #e8f5e9 !important; /* 更浅的绿色 */
  transition: background-color 0.3s ease; /* 平滑过渡效果 */
}
</style>
