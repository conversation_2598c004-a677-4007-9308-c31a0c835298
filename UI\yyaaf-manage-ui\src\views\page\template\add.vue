<template>
  <div class="app-container">
    <el-card>
      <!--<div class="page-header">
        <h2>{{ title }}</h2>
      </div>-->
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
        <el-row>
          <el-col :span="24">
            <el-form-item label="抓取URL" prop="originalUrl">
              <el-input
                v-model="form.originalUrl"
                placeholder="请输入抓取链接"
                style="width: 80%"
              />
              <el-button @click="spiderPage" type="primary">抓取</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="模板标识" prop="tempId">
              <el-input
                v-model="form.tempId"
                placeholder="请输入模板标识，抓取文章等情况下不要修改"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类别" prop="cid">
              <x-select v-model="form.cId" url="/category/options" style="width: 100%"></x-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否启用" prop="enable">
              <x-radio v-model="form.enable" button :options="enableOptions" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容" prop="content">
              <el-card shadow="never" style="border-radius: 8px; padding: 10px">
                <div class="editor-header">
                  <span class="editor-mode">{{ showSourceCode ? '源码模式' : '编辑模式' }}</span>
                  <el-button type="primary" size="small" @click="toggleSourceMode">
                    {{ showSourceCode ? '切换到编辑模式' : '切换到源码模式' }}
                  </el-button>
                </div>

                <tinymce
                  v-if="!showSourceCode"
                  v-model="form.content"
                  :upload-temp="uploadTemp"
                  :uploadTempId="uploadTempId"
                />
                <textarea v-else v-model="form.content" class="source-code-textarea" />
              </el-card>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 按钮区域 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { enableOptions } from '@/utils/options'
import Tinymce from '@/components/Tinymce'
import { templateApi } from '@/api'
import { randomString } from '@/utils'

export default {
  components: {
    Tinymce,
  },
  data() {
    return {
      title: '新增模板',
      form: {
        enable: true,
        tempId: randomString(32),
      },
      rules: {
        name: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        tempId: [{ required: true, message: '请输入模板标识', trigger: 'blur' }],
      },
      uploadTemp: 'preview',
      enableOptions,
      showSourceCode: false,
    }
  },
  created() {
    // 可选：如果你传了 id 可以在页面加载时调用 edit()
    const id = this.$route.query.id
    if (id) {
      this.edit({ id })
    }
  },
  computed: {
    // 计算属性可以根据需要添加
    uploadTempId() {
      return this.form.tempId
    },
  },
  methods: {
    async edit(row) {
      this.title = '修改模板'
      this.$xloading.show()
      const res = await templateApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
        this.uploadTemp = 'release'
      } else {
        this.$xMsgError('加载失败！' + res.msg)
      }
      this.$xloading.hide()
    },

    reset() {
      this.form = {
        name: undefined,
        content: '',
        enable: true,
        tempId: randomString(32),
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          const res = id
            ? await templateApi.editTemplate(data)
            : await templateApi.addTemplate(data)
          this.$xloading.hide()

          if (res.code == 0) {
            this.$xMsgSuccess(id ? '修改成功' : '添加成功')
            // 提交成功后的处理：跳转或清空表单
            this.close()
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        }
      })
    },

    async spiderPage() {
      this.$xloading.show()
      const data = Object.assign({}, this.form)
      const res = await templateApi.spiderTemplate(data)
      if (res && res.code == 0) {
        this.form.content = res.data.content
        this.uploadTemp = 'preview'
      } else {
        this.$xMsgError('抓取失败！' + res.msg)
      }
      this.$xloading.hide()
    },

    toggleSourceMode() {
      this.showSourceCode = !this.showSourceCode
    },
    // 关闭编辑器
    close() {
      this.$router.push({ path: '/page/template' })
    },
  },
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.editor-mode {
  font-weight: bold;
  font-size: 14px;
}

.source-code-textarea {
  width: 100%;
  height: 800px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  outline: none;
  resize: vertical;
  background: #f9f9f9;
}
.source-code-textarea:focus {
  border-color: #409eff;
  background: #fff;
}
</style>
