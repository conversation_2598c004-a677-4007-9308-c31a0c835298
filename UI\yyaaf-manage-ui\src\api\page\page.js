import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/page/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/page/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addPage(data) {
  return request({
    url: '/page/add',
    method: 'post',
    data: data,
  })
}

export function editPage(data) {
  return request({
    url: '/page/edit',
    method: 'post',
    data: data,
  })
}

export function delPage(id) {
  return request({
    url: '/page/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function preview(id) {
  return request({
    url: '/page/preview',
    method: 'get',
    params: {
      id,
    },
  })
}

export function deliverUrl(id) {
  return request({
    url: '/page/deliverUrl',
    method: 'get',
    params: {
      id,
    },
  })
}

//----------Page结束----------
