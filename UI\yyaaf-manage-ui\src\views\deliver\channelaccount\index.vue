<template>
  <div class="app-container">
    <el-card>
      <!--<el-alert title="请确认要调粉的任务" type="info" show-icon />-->
      <p style="margin: 0 0 16px 0"><strong>任务名称：</strong>{{ taskTitle }}</p>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="渠道标识">
          <el-input v-model="queryParams.cId"></el-input>
        </el-form-item>
        <el-form-item label="主号">
          <el-input v-model="queryParams.account"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <x-select v-model="queryParams.isEnable" :options="enableOptions"></x-select>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-document-add"
          @click="handleAdd"
          v-permission="['deliverTaskChannelAccount:add']"
          >加号</el-button
        >
        <el-button
          size="mini"
          type="warning"
          icon="el-icon-edit"
          @click="handleChangeFans"
          v-permission="['deliverTaskChannelAccount:edit']"
          >调粉</el-button
        >
        <el-button
          size="mini"
          type="warning"
          icon="el-icon-edit"
          @click="handleBetchChangeAccount"
          v-permission="['deliverTaskChannelAccount:change']"
          >批量换号</el-button
        >
        <el-button
          size="mini"
          type="danger"
          icon="el-icon-download"
          @click="handleBatchForceOff"
          v-permission="['deliverTaskChannelAccount:edit']"
          >批量强制下线</el-button
        >
        <el-button
          size="mini"
          type="success"
          icon="el-icon-upload2"
          @click="handleRecoverBatchForceOff"
          v-permission="['deliverTaskChannelAccount:edit']"
          >批量恢复强制下线</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" align="center" width="50"></el-table-column>
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <!--<el-table-column
          prop="taskName"
          label="任务名称"
          align="center"
          min-width="200"
        ></el-table-column>-->
        <el-table-column prop="account" label="主号" align="center" min-width="150">
          <template v-slot="{ row }">
            <el-link type="primary" :underline="false" @click="handleGotoCounter(row.account)">
              <div>{{ row.account }}</div>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="渠道" align="center" min-width="200">
          <template v-slot="{ row }">
            <div class="table-cell-content">
              <span class="sc-id">{{ row.cId }}</span>
              <br />
              <span class="sc-name">{{ row.cName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="target" label="目标/进线/重复" align="center" min-width="150">
          <template v-slot="{ row }">
            {{ row.target }} / {{ row.newFans }} / {{ row.repeatFans }}
          </template>
        </el-table-column>
        <el-table-column prop="target" label="进线率" align="center" min-width="100">
          <template v-slot="{ row }"> {{ row.newFansPer }}% </template>
        </el-table-column>
        <el-table-column prop="target" label="IP/UV/PV" align="center" min-width="150">
          <template v-slot="{ row }">
            <el-link type="primary" :underline="false" @click="handlePageCollectRecord(row)">
              <span>{{ row.ip }} / {{ row.uv }} / {{ row.pv }}</span>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="权重" align="center" min-width="150">
          <template slot-scope="scope">
            <template>
              <el-input-number
                v-model.number="scope.row.weight"
                size="small"
                placeholder="请输入数字(限数字)"
                @change="onWeightChange(scope.row)"
              />
            </template>
          </template>
        </el-table-column>
        <el-table-column label="强制进线" align="center" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.forceIn"
              :active-value="true"
              :inactive-value="false"
              active-color="#13ce66"
              @change="onForceInChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="强制下线" align="center" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.forceOff"
              :active-value="true"
              :inactive-value="false"
              active-color="#13ce66"
              @change="onForceOffChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.isEnable">上线</el-tag>
            <el-tag type="danger" size="medium" v-else>下线</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="原因" align="center" min-width="150">
          <template v-slot="{ row }">
            <div v-if="row.disableReason">
              <el-tag effect="plain" type="success" size="medium" v-if="row.disableReason == 1"
                >调粉</el-tag
              >
              <el-tag effect="plain" type="danger" size="medium" v-else-if="row.disableReason == 2"
                >封禁</el-tag
              >
              <el-tag effect="plain" type="info" size="medium" v-else-if="row.disableReason == 3"
                >任务</el-tag
              >
              <el-tag effect="plain" type="warning" size="medium" v-else-if="row.disableReason == 5"
                >未找到</el-tag
              >
              <el-tag effect="plain" type="warning" size="medium" v-else-if="row.disableReason == 6"
                >强制下线</el-tag
              >
              <el-tag effect="plain" type="warning" size="medium" v-else-if="row.disableReason == 7"
                >换号</el-tag
              >
              <el-tag effect="plain" type="info" size="medium" v-else>弃用</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="changeTime"
          label="修改时间"
          sortable
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column label="操作" align="center" width="120" fixed="left">
          <template slot-scope="scope">
            <el-button size="mini" type="warning" @click="handleChangeAccount(scope.row)"
              >换号</el-button
            >
          </template>
        </el-table-column>
        <!--<el-table-column
          prop="fromChange"
          label="调粉Id"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="changeNewFans"
          label="调粉数"
          sortable
          align="center"
          min-width="100"
        ></el-table-column>-->
      </x-table>
    </el-card>

    <change-fans-dialog ref="changeFansDialog" @ok="handleOk" />
    <add-dialog ref="addDialog" @ok="handleOk" />
    <change-account-dialog ref="changeAccountDialog" @ok="handleOk" />
    <betch-change-account ref="betchChangeAccount" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { deliverTaskChannelAccountApi } from '@/api'
import ChangeFansDialog from './changefans'
import AddDialog from './add'
import ChangeAccountDialog from './changeaccount'
import BetchChangeAccount from './betchchangeaccount'

export default {
  components: {
    ChangeFansDialog,
    AddDialog,
    ChangeAccountDialog,
    BetchChangeAccount,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      taskid: -1,
      taskTitle: '',
      // 选中数组
      accounts: [],
      // 选中的完整行数据
      selectedRows: [],
      queryParams: {
        isEnable: null,
      },
      loading: false,
      tableData: {},
      enableOptions: [
        { label: '全部', value: null },
        { label: '启用', value: true },
        { label: '禁用', value: false },
      ],
    }
  },
  created() {
    let query = this.$router.history.current.query
    if (query.taskid) {
      this.taskid = query.taskid
      this.queryParams.taskid = this.taskid
      this.taskTitle = query.taskTitle
    } else this.$xMsgWarning('需要携带任务Id参数！')
  },
  methods: {
    queryReset() {
      this.queryParams = {
        isEnable: null,
      }
      this.queryParams.taskid = this.taskid
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await deliverTaskChannelAccountApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    async handleAdd() {
      this.$refs.addDialog.add(this.queryParams.taskid)
    },
    handleChangeFans() {
      if (this.accounts.length > 0) {
        this.$refs.changeFansDialog.edit(this.accounts, this.queryParams.taskid)
      } else {
        this.$xMsgWarning('请选择要调粉的记录！')
      }
    },
    handleOk() {
      this.$refs.table.refresh()
    },
    handleSelectionChange(selection) {
      this.accounts = selection.map((item) => item.account)
      this.selectedRows = selection
    },
    handleChangeAccount(row) {
      this.$refs.changeAccountDialog.open(row)
    },
    handleBetchChangeAccount() {
      const row = {
        taskId: this.taskid,
        taskTitle: this.taskTitle,
      }
      this.$refs.betchChangeAccount.open(row)
    },
    onForceInChange(row) {
      let statusMsg = row.forceIn == true ? '启用' : '禁用'
      this.$confirm(`是否确认【${statusMsg}】强制进线状态？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await deliverTaskChannelAccountApi.changeForceIn(row.id, row.forceIn)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
          this.$refs.table.refresh()
        })
        .catch(() => {
          this.$xloading.hide()
          this.$refs.table.refresh()
        })
    },
    onForceOffChange(row) {
      let statusMsg = row.forceOff == true ? '启用' : '禁用'
      this.$confirm(`是否确认【${statusMsg}】强制下线状态？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await deliverTaskChannelAccountApi.changeForceOff(row.id, row.forceOff)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
          this.$refs.table.refresh()
        })
        .catch(() => {
          this.$xloading.hide()
          this.$refs.table.refresh()
        })
    },
    async onWeightChange(row) {
      this.$xloading.show()
      var res = await deliverTaskChannelAccountApi.changeWeight(row.id, row.weight)
      this.$xloading.hide()
      if (res.code == 0) {
        this.$xMsgSuccess('操作成功')
      } else {
        this.$xMsgError('操作失败！' + res.msg)
      }
      this.$refs.table.refresh()
    },

    handleGotoCounter(account) {
      //this.$router.push({ path: '/counter/counterorderdaterecords', query: { account: account } })
      window.open(`/counter/counterorderdaterecords?account=${account}`, '_blank')
    },

    handlePageCollectRecord(row) {
      this.$router.push({
        path: '/pagestats/pagecollectrecord',
        query: {
          date: row.date,
          type: 2,
          areaId: row.areaId,
          taskConfigId: row.taskConfigId,
          cid: row.cId,
          account: row.account,
        },
      })
    },

    // 批量恢复强制下线
    async handleRecoverBatchForceOff() {
      if (this.selectedRows.length === 0) {
        this.$xMsgWarning('请选择要操作的记录！')
        return
      }

      const accountList = this.selectedRows.map((row) => row.account).join('、')
      this.$confirm(
        `是否确认对选中的 ${this.selectedRows.length} 个主号【${accountList}】执行恢复强制下线操作？`,
        '批量恢复强制下线确认',
        {
          type: 'warning',
          confirmButtonText: '确认执行',
          cancelButtonText: '取消',
        }
      )
        .then(async () => {
          this.$xloading.show()

          let successCount = 0
          let failCount = 0
          const failedAccounts = []

          try {
            // 循环调用单个接口
            for (const row of this.selectedRows) {
              try {
                const res = await deliverTaskChannelAccountApi.changeForceOff(row.id, false)
                if (res.code === 0) {
                  successCount++
                } else {
                  failCount++
                  failedAccounts.push(`${row.account}(${res.msg})`)
                }
              } catch (error) {
                failCount++
                failedAccounts.push(`${row.account}(${error.message})`)
              }
            }

            this.$xloading.hide()

            // 显示操作结果
            if (failCount === 0) {
              this.$xMsgSuccess(`批量恢复强制下线操作成功，共处理 ${successCount} 个主号`)
            } else if (successCount === 0) {
              this.$xMsgError(`批量恢复强制下线操作失败，${failCount} 个主号操作失败`)
            } else {
              this.$xMsgWarning(
                `批量恢复强制下线操作完成，成功 ${successCount} 个，失败 ${failCount} 个。失败详情：${failedAccounts.join(
                  '、'
                )}`
              )
            }

            this.$refs.table.refresh()
          } catch (error) {
            this.$xloading.hide()
            this.$xMsgError('批量恢复强制下线操作失败：' + error.message)
          }
        })
        .catch(() => {
          // 用户取消操作
        })
    },

    // 批量强制下线
    async handleBatchForceOff() {
      if (this.selectedRows.length === 0) {
        this.$xMsgWarning('请选择要操作的记录！')
        return
      }

      const accountList = this.selectedRows.map((row) => row.account).join('、')
      this.$confirm(
        `是否确认对选中的 ${this.selectedRows.length} 个主号【${accountList}】执行批量强制下线操作？`,
        '批量强制下线确认',
        {
          type: 'warning',
          confirmButtonText: '确认执行',
          cancelButtonText: '取消',
        }
      )
        .then(async () => {
          this.$xloading.show()

          let successCount = 0
          let failCount = 0
          const failedAccounts = []

          try {
            // 循环调用单个接口
            for (const row of this.selectedRows) {
              try {
                const res = await deliverTaskChannelAccountApi.changeForceOff(row.id, true)
                if (res.code === 0) {
                  successCount++
                } else {
                  failCount++
                  failedAccounts.push(`${row.account}(${res.msg})`)
                }
              } catch (error) {
                failCount++
                failedAccounts.push(`${row.account}(${error.message})`)
              }
            }

            this.$xloading.hide()

            // 显示操作结果
            if (failCount === 0) {
              this.$xMsgSuccess(`批量强制下线操作成功，共处理 ${successCount} 个主号`)
            } else if (successCount === 0) {
              this.$xMsgError(`批量强制下线操作失败，${failCount} 个主号操作失败`)
            } else {
              this.$xMsgWarning(
                `批量强制下线操作完成，成功 ${successCount} 个，失败 ${failCount} 个。失败详情：${failedAccounts.join(
                  '、'
                )}`
              )
            }

            this.$refs.table.refresh()
          } catch (error) {
            this.$xloading.hide()
            this.$xMsgError('批量强制下线操作失败：' + error.message)
          }
        })
        .catch(() => {
          // 用户取消操作
        })
    },
  },
}
</script>

<style scoped>
/* 样式1：调整字体大小、行高、间距 */
.table-cell-content {
  display: flex;
  flex-direction: column;
  align-items: center; /* 中心对齐 */
  font-size: 14px; /* 设置字体大小 */
  line-height: 1; /* 行高 */
  word-break: break-word; /* 防止长单词溢出 */
  color: #333; /* 设置字体颜色 */
}

.sc-id {
  font-weight: bold; /* scId 字段加粗 */
  color: #409eff; /* 修改颜色 */
}

.sc-name {
  font-style: italic; /* scName 字段斜体 */
  color: #67c23a; /* 修改颜色 */
}

/* 样式2：给换行部分添加背景色 */
.table-cell-content span {
  background-color: #f2f4f5; /* 设置背景色 */
  padding: 0px 4px; /* 给文字加点内边距 */
  border-radius: 4px; /* 设置圆角 */
}

.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
</style>
