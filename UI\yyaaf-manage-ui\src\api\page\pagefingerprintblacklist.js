import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/pageFingerprintBlackList/getList',
    method: 'get',
    params,
  })
}

export function getDetail(fingerprintId) {
  return request({
    url: '/pageFingerprintBlackList/getDetail',
    method: 'get',
    params: {
      fingerprintId,
    },
  })
}

export function addPageFingerprintBlackList(data) {
  return request({
    url: '/pageFingerprintBlackList/add',
    method: 'post',
    data: data,
  })
}

export function editPageFingerprintBlackList(data) {
  return request({
    url: '/pageFingerprintBlackList/edit',
    method: 'post',
    data: data,
  })
}

export function delPageFingerprintBlackList(fingerprintId) {
  return request({
    url: '/pageFingerprintBlackList/delete',
    method: 'post',
    data: {
      fingerprintId,
    },
  })
}

//----------PageFingerprintBlackList结束----------
