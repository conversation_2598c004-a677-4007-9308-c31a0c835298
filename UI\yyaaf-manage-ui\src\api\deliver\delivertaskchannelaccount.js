import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/deliverTaskChannelAccount/getList',
    method: 'get',
    params,
  })
}

export function add(data) {
  return request({
    url: '/deliverTaskChannelAccount/add',
    method: 'post',
    data: data,
  })
}

export function change(data) {
  return request({
    url: '/deliverTaskChannelAccount/change',
    method: 'post',
    data: data,
  })
}

export function betchChange(data) {
  return request({
    url: '/deliverTaskChannelAccount/betchChange',
    method: 'post',
    data: data,
  })
}

export function changeWeight(id, weight) {
  return request({
    url: '/deliverTaskChannelAccount/changeWeight',
    method: 'post',
    data: {
      id,
      weight,
    },
  })
}

export function changeForceIn(id, forceIn) {
  return request({
    url: '/deliverTaskChannelAccount/changeForceIn',
    method: 'post',
    data: {
      id,
      forceIn,
    },
  })
}

export function changeForceOff(id, forceOff) {
  return request({
    url: '/deliverTaskChannelAccount/changeForceOff',
    method: 'post',
    data: {
      id,
      forceOff,
    },
  })
}

//----------DeliverTaskChannelAccount结束----------
