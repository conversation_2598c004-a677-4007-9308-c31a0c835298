import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/fBUser/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/fBUser/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addFBUser(data) {
  return request({
    url: '/fBUser/add',
    method: 'post',
    data: data,
  })
}

export function editFBUser(data) {
  return request({
    url: '/fBUser/edit',
    method: 'post',
    data: data,
  })
}

export function delFBUser(id) {
  return request({
    url: '/fBUser/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function getOAthUrl(id) {
  return request({
    url: '/fBUser/getOAthUrl',
    method: 'post',
    data: {
      id,
    },
  })
}

export function getOptions(params) {
  return request({
    url: '/fBUser/options',
    method: 'get',
    params,
  })
}
//----------F<PERSON><PERSON>ser结束----------
