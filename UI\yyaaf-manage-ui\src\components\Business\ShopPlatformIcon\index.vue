<template>
  <div class="icon-container">
    <svg-icon class="platform-icon" icon-class="taobao" v-if="platform == 1" />
    <svg-icon class="platform-icon" icon-class="pdd" v-else-if="platform == 2" />
    <svg-icon class="platform-icon" icon-class="jd" v-else-if="platform == 3" />
    <svg-icon class="platform-icon" icon-class="suning" v-else-if="platform == 5" />
    <svg-icon class="platform-icon" icon-class="tmall" v-else-if="platform == 6" />
  </div>
</template>

<script>
export default {
  props: {
    platform: Number,
  },
}
</script>

<style lang="scss" scoped>
$platform-icon: 30px;

.icon-container {
  display: inline-block;
}

.platform-icon {
  width: $platform-icon;
  height: $platform-icon;
}
</style>
