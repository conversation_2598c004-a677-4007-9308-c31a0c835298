<template>
  <div class="notice-container">
    <el-dropdown class="dropdown">
      <span class="el-dropdown-link">
        <!-- 通知图标 -->
        <i class="el-icon-bell notice-icon"></i>
        <el-badge :value="unReadCount" :max="99" v-show="visible"></el-badge>
      </span>
      <el-dropdown-menu slot="dropdown">
        <router-link to="/systemnotice/noticelist">
          <el-dropdown-item> 消息列表 </el-dropdown-item>
        </router-link>
        <el-dropdown-item divided @click.native="handleReadAll"> 一键已读 </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { systemNoticeApi } from '@/api'
export default {
  data() {
    return {}
  },
  computed: {
    unReadCount() {
      return this.$store.state.notice.unReadCount
    },
    visible() {
      return this.unReadCount > 0 // 直接计算是否有未读
    },
  },
  methods: {
    async handleReadAll() {
      this.$xloading.show()
      const res = await systemNoticeApi.readAll()
      this.$xloading.hide()
      if (res.code == 0) {
        this.$xMsgSuccess('操作成功')
        window.location.reload()
      } else {
        this.$xMsgError('操作失败！' + res.msg)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.notice-container {
  position: relative;
  top: -12px;
  display: inline-block;
  height: 100%;
  cursor: pointer; /* 🔥 添加点击手势 */

  margin-right: 20px;
  .notice-icon {
    font-size: 20px;
    color: #409eff;
  }
}
</style>
