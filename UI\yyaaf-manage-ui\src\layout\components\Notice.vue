<template>
  <div class="notice-container" @click="handleNoticeClick">
    <!-- 通知图标 -->
    <i class="el-icon-bell notice-icon"></i>
    <el-badge :value="unReadCount" :max="99" v-show="visible"></el-badge>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  computed: {
    unReadCount() {
      return this.$store.state.notice.unReadCount
    },
    visible() {
      return this.unReadCount > 0 // 直接计算是否有未读
    },
  },
  methods: {
    handleNoticeClick() {
      this.$router.push({ path: '/systemnotice/noticelist' })
    },
  },
}
</script>
<style lang="scss" scoped>
.notice-container {
  position: relative;
  top: -12px;
  display: inline-block;
  height: 100%;
  cursor: pointer; /* 🔥 添加点击手势 */

  margin-right: 20px;
  .notice-icon {
    font-size: 20px;
    color: #409eff;
  }
}
</style>
