<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="queryParams.type" placeholder="">
            <el-option
              v-for="item in ipTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="">
            <el-option
              v-for="item in statuOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点击标识">
          <el-input v-model="queryParams.pageIdentifier"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="date" label="日期" align="center" min-width="100">
          <template v-slot="{ row }">
            <span>{{ row.date.slice(0, 10) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pageIdentifier" label="点击标识" align="center" min-width="400">
          <template v-slot="{ row }">
            <span style="font-size: 12px">{{ row.pageIdentifier }}</span>
          </template>
        </el-table-column>
        <el-table-column label="IP列表" align="center" min-width="350">
          <template v-slot="{ row }">
            <div class="ip-list-container">
              <!-- IP数量统计 -->
              <div class="ip-count-header">
                <el-tag type="info" size="mini" effect="plain">
                  <i class="el-icon-connection"></i>
                  共 {{ row.count }} 个IP
                </el-tag>
                <el-button
                  v-if="row.ips && row.ips.length > 3"
                  type="text"
                  size="mini"
                  @click="toggleIpExpand(row)"
                  class="expand-btn"
                >
                  {{ row.expanded ? '收起' : '展开全部' }}
                  <i :class="row.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                </el-button>
              </div>

              <!-- IP列表显示 -->
              <div class="ip-tags-container" v-if="row.ips && row.ips.length > 0">
                <template v-if="row.ips.length <= 3 || row.expanded">
                  <!-- 显示所有IP -->
                  <el-tag
                    v-for="(ip, index) in row.ips"
                    :key="index"
                    :type="getIpTagType(index)"
                    size="small"
                    class="ip-tag"
                    effect="light"
                  >
                    <i class="el-icon-location-outline"></i>
                    {{ ip }}
                  </el-tag>
                </template>
                <template v-else>
                  <!-- 只显示前3个IP -->
                  <el-tag
                    v-for="(ip, index) in row.ips.slice(0, 3)"
                    :key="index"
                    :type="getIpTagType(index)"
                    size="small"
                    class="ip-tag"
                    effect="light"
                  >
                    <i class="el-icon-location-outline"></i>
                    {{ ip }}
                  </el-tag>
                  <el-tag type="warning" size="small" class="more-tag">
                    +{{ row.ips.length - 3 }} 更多
                  </el-tag>
                </template>
              </div>

              <!-- 空状态 -->
              <div v-else class="empty-ip-list">
                <el-tag type="info" size="small" effect="plain">
                  <i class="el-icon-warning-outline"></i>
                  暂无IP数据
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.type == 1" type="info">访问</el-tag>
            <el-tag v-if="row.type == 2" type="primary">点击</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.status == 0" type="success">正常</el-tag>
            <el-tag v-else-if="row.status == 1" type="danger">异常</el-tag>
            <el-tag v-else type="danger">黑名单</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="identifierStatus" label="标识状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.identifierStatus == 0" type="success">正常</el-tag>
            <el-tag v-else type="danger">黑名单</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center">
          <template v-slot="{ row }">
            <el-button
              v-if="row.identifierStatus == 0"
              size="mini"
              type="primary"
              @click="handleAddToIdentifierBlackList(row)"
              v-permission="['pagehostdate:addblack']"
              >添加标识黑名单</el-button
            >
            <el-button
              v-if="row.identifierStatus == 1"
              size="mini"
              type="warning"
              @click="handleRemoveFromIdentifierBlackList(row)"
              v-permission="['pagehostdate:removeblack']"
              >移除标识黑名单</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { pageIdentifierDateApi, pageHostDateApi } from '@/api'
import { ipTypeOptions } from '@/utils/options'
import { getLocalISODateString } from '@/utils/index'

export default {
  components: {},
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        date: getLocalISODateString(),
        type: -1,
        status: -1,
      },
      loading: false,
      tableData: {},
      ipTypeOptions,
      statuOptions: [
        { label: '全部', value: -1 },
        { label: '正常', value: 0 },
        { label: '异常', value: 1 },
      ],
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {
        date: getLocalISODateString(),
        type: -1,
        status: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await pageIdentifierDateApi.getList(params)
      // 为每行数据添加展开状态
      if (res.data && res.data.length > 0) {
        res.data.forEach((row) => {
          this.$set(row, 'expanded', false)
        })
      }
      this.tableData = res
      this.$xloading.hide()
    },

    // 切换IP列表展开/收起状态
    toggleIpExpand(row) {
      this.$set(row, 'expanded', !row.expanded)
    },

    // 根据索引获取IP标签类型，提供视觉区分
    getIpTagType(index) {
      const types = ['primary', 'success', 'warning', 'danger', 'info']
      return types[index % types.length]
    },
    async handleAddToIdentifierBlackList(row) {
      this.$confirm(`是否确认添加【${row.pageIdentifier}】到标识黑名单？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          row.blackType = 2
          const res = await pageHostDateApi.addToBlackList(row)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    async handleRemoveFromIdentifierBlackList(row) {
      this.$confirm(`是否确认从标识黑名单中移除【${row.pageIdentifier}】？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          row.blackType = 2
          const res = await pageHostDateApi.removeBlackList(row)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
  },
}
</script>

<style scoped>
/* IP列表容器样式 */
.ip-list-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 8px 4px;
  max-width: 100%;
}

/* IP数量统计头部 */
.ip-count-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 4px;
  width: 100%;
}

/* 展开按钮样式 */
.expand-btn {
  padding: 2px 6px !important;
  font-size: 11px !important;
  color: #409eff !important;
  border: none !important;
  background: none !important;
}

.expand-btn:hover {
  color: #66b1ff !important;
  background-color: #ecf5ff !important;
}

/* IP标签容器 */
.ip-tags-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 4px;
  max-width: 100%;
  line-height: 1.2;
}

/* IP标签样式 */
.ip-tag {
  font-size: 11px !important;
  padding: 2px 6px !important;
  margin: 1px !important;
  border-radius: 3px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.ip-tag i {
  font-size: 10px;
  margin-right: 2px;
}

/* 更多标签样式 */
.more-tag {
  font-size: 11px !important;
  padding: 2px 6px !important;
  margin: 1px !important;
  border-radius: 3px !important;
  font-weight: 600;
  background-color: #fdf6ec !important;
  border-color: #f5dab1 !important;
  color: #e6a23c !important;
}

/* 空状态样式 */
.empty-ip-list {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: #909399;
  font-size: 12px;
}

/* IP标签颜色主题 */
.ip-tag.el-tag--primary {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.ip-tag.el-tag--success {
  background-color: #f0f9ff;
  border-color: #b3e19d;
  color: #67c23a;
}

.ip-tag.el-tag--warning {
  background-color: #fdf6ec;
  border-color: #f5dab1;
  color: #e6a23c;
}

.ip-tag.el-tag--danger {
  background-color: #fef0f0;
  border-color: #fbc4c4;
  color: #f56c6c;
}

.ip-tag.el-tag--info {
  background-color: #f4f4f5;
  border-color: #d3d4d6;
  color: #909399;
}

/* 悬停效果 */
.ip-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ip-list-container {
    padding: 6px 2px;
    gap: 6px;
  }

  .ip-count-header {
    flex-direction: column;
    gap: 4px;
  }

  .ip-tags-container {
    gap: 2px;
  }

  .ip-tag {
    font-size: 10px !important;
    padding: 1px 4px !important;
    max-width: 100px;
  }

  .expand-btn {
    font-size: 10px !important;
    padding: 1px 4px !important;
  }
}

/* 保留原有的截断标签样式（向后兼容） */
.truncate-tag {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
</style>
