<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="queryParams.type" placeholder="">
            <el-option
              v-for="item in ipTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="">
            <el-option
              v-for="item in statuOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点击标识">
          <el-input v-model="queryParams.pageIdentifier"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="date" label="日期" align="center" min-width="100">
          <template v-slot="{ row }">
            <span>{{ row.date.slice(0, 10) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pageIdentifier" label="点击标识" align="center" min-width="400">
          <template v-slot="{ row }">
            <span style="font-size: 12px">{{ row.pageIdentifier }}</span>
          </template>
        </el-table-column>
        <el-table-column label="IP列表" align="center" min-width="300">
          <template v-slot="{ row }">
            <el-select>
              <el-option v-for="ip in row.ips" :key="ip" :label="ip" :value="ip"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          prop="count"
          label="不同IP数"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column prop="type" label="类型" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.type == 1" type="info">访问</el-tag>
            <el-tag v-if="row.type == 2" type="primary">点击</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.status == 0" type="success">正常</el-tag>
            <el-tag v-else-if="row.status == 1" type="danger">异常</el-tag>
            <el-tag v-else type="danger">黑名单</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="identifierStatus" label="标识状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.identifierStatus == 0" type="success">正常</el-tag>
            <el-tag v-else type="danger">黑名单</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center">
          <template v-slot="{ row }">
            <el-button
              v-if="row.identifierStatus == 0"
              size="mini"
              type="primary"
              @click="handleAddToIdentifierBlackList(row)"
              v-permission="['pagehostdate:addblack']"
              >添加标识黑名单</el-button
            >
            <el-button
              v-if="row.identifierStatus == 1"
              size="mini"
              type="warning"
              @click="handleRemoveFromIdentifierBlackList(row)"
              v-permission="['pagehostdate:removeblack']"
              >移除标识黑名单</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { pageIdentifierDateApi, pageHostDateApi } from '@/api'
import { ipTypeOptions } from '@/utils/options'

export default {
  components: {},
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        date: new Date().toISOString().slice(0, 10),
        type: -1,
        status: -1,
      },
      loading: false,
      tableData: {},
      ipTypeOptions,
      statuOptions: [
        { label: '全部', value: -1 },
        { label: '正常', value: 0 },
        { label: '异常', value: 1 },
      ],
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {
        date: new Date().toISOString().slice(0, 10),
        type: -1,
        status: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await pageIdentifierDateApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    async handleAddToIdentifierBlackList(row) {
      this.$confirm(`是否确认添加【${row.pageIdentifier}】到标识黑名单？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          row.blackType = 2
          const res = await pageHostDateApi.addToBlackList(row)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    async handleRemoveFromIdentifierBlackList(row) {
      this.$confirm(`是否确认从标识黑名单中移除【${row.pageIdentifier}】？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          row.blackType = 2
          const res = await pageHostDateApi.removeBlackList(row)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
  },
}
</script>

<style scoped>
.truncate-tag {
  max-width: 300px; /* 根据需要调整宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
</style>
