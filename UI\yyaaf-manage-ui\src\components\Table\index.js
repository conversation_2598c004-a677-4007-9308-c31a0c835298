import { Table, Message } from 'element-ui'
export default {
  props: Object.assign({}, Table.props, {
    page: {
      type: <PERSON>olean,
      default: true,
    },
    pageSize: {
      type: Number,
      default: 50,
    },
    data: {
      type: Object,
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    loadData: {},
  }),
  data() {
    return {
      currentPage: 1,
      pageSize: this.pageSize,
      pageSizes: [20, 50, 100, 200, 500],
      pageLayout: 'total, sizes, prev, pager, next, jumper',
    }
  },
  created() {
    // 读取数据
    if (this.autoLoad) {
      this.loadTableData(this.currentPage, this.pageSize)
    }
  },
  watch: {
    data(res) {
      if (res && res.code != 0) {
        Message({
          type: 'error',
          message: res.msg || '请求出错',
        })
      }
    },
  },
  methods: {
    updatePageUI(page, size) {
      // console.log(arguments)
      this.currentPage = page
      this.pageSize = size
    },
    refresh(reload = false) {
      if (reload) {
        this.currentPage = 1
      }
      this.loadTableData()
    },
    loadTableData() {
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
      }
      this.loadData(params)
    },
    handleSizeChange(value) {
      this.pageSize = value
      this.loadTableData()
    },
    handleCurrentChange(value) {
      this.currentPage = value
      this.loadTableData()
    },
    handlePrevClick(value) {
      this.currentPage = value
      this.loadTableData()
    },
    handleNextClick(value) {
      this.currentPage = value
      this.loadTableData()
    },
  },
  render() {
    const props = {}
    Object.keys(Table.props).forEach((k) => {
      // console.log(k)
      if (k == 'data') {
        this[k] && (props[k] = this[k].data)
      } else {
        this[k] && (props[k] = this[k])
      }

      return props[k]
    })
    const res = this.data
    const table = (
      <el-table
        {...{
          props,
          on: this.$listeners,
        }}
      >
        {Object.keys(this.$slots).map((name) => (
          <template slot={name}>{this.$slots[name]}</template>
        ))}
      </el-table>
    )
    const pageEl = (
      <el-pagination
        background={true}
        layout={this.pageLayout}
        pageSizes={this.pageSizes}
        total={this.data.total}
        pageSize={this.pageSize}
        {...{
          on: {
            'size-change': this.handleSizeChange,
            'current-change': this.handleCurrentChange,
            'prev-click': this.handlePrevClick,
            'next-click': this.handleNextClick,
          },
        }}
      />
    )

    const backtop = <el-backtop></el-backtop>

    return (
      <div>
        {table}
        {this.page ? pageEl : ''}
        {backtop}
      </div>
    )
  },
}
