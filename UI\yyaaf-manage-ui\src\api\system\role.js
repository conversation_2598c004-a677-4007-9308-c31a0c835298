import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/system/role/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/system/role/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function getRoleMenuTree(systemCode) {
  return request({
    url: '/system/role/roleMenuTree',
    method: 'get',
    params: {
      systemCode,
    },
  })
}

export function editRole(data) {
  return request({
    url: '/system/role/edit',
    method: 'post',
    data,
  })
}

export function addRole(data) {
  return request({
    url: '/system/role/add',
    method: 'post',
    data,
  })
}

export function deleteRole(id) {
  return request({
    url: '/system/role/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function changeStatus(id, status) {
  return request({
    url: '/system/role/changeStatus',
    method: 'post',
    data: {
      id,
      status,
    },
  })
}
