import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/fBBusinesses/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/fBBusinesses/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addFBBusinesses(data) {
  return request({
    url: '/fBBusinesses/add',
    method: 'post',
    data: data,
  })
}

export function editFBBusinesses(data) {
  return request({
    url: '/fBBusinesses/edit',
    method: 'post',
    data: data,
  })
}

export function delFBBusinesses(id) {
  return request({
    url: '/fBBusinesses/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------FBBusinesses结束----------
