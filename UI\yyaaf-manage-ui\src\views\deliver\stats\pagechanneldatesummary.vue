<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="地区">
          <x-select show-default v-model="queryParams.areaId" url="/deliverArea/options"></x-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="queryParams.type" placeholder="">
            <el-option
              v-for="item in ipTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar"> </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        :page="false"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :show-summary="true"
        :summary-method="getSummaries"
      >
        >
        <el-table-column label="日期" align="center" min-width="130">
          <template v-slot="{ row }">
            <span>{{ row.date.slice(0, 10) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="地区" align="center" min-width="200">
          <template v-slot="{ row }">
            <span>{{ row.areaName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="渠道" align="center" min-width="200">
          <template v-slot="{ row }">
            <channel-info :cId="row.cId" :cName="row.cName"></channel-info>
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.type == 1" type="info">访问</el-tag>
            <el-tag v-if="row.type == 2" type="primary">点击</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="进线">
          <el-table-column label="IP/UV/PV" align="center" min-width="150">
            <template v-slot="{ row }">
              <el-link type="primary" :underline="false" @click="handlePageCollectRecord(row)">
                <span>{{ row.ip }} / {{ row.uv }} / {{ row.pv }}</span>
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            prop="newFans"
            label="进线"
            sortable
            align="center"
            min-width="100"
          ></el-table-column>
          <el-table-column label="进线率" sortable align="center" min-width="100">
            <template v-slot="{ row }">
              <span>{{ row.newFansPer }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="未知">
          <el-table-column
            prop="none"
            label="未知"
            sortable
            align="center"
            min-width="100"
          ></el-table-column>
          <el-table-column label="比例" sortable align="center" min-width="100">
            <template v-slot="{ row }">
              <span>{{ row.nonePer }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="FB">
          <el-table-column
            prop="fb"
            label="FB"
            sortable
            align="center"
            min-width="100"
          ></el-table-column>
          <el-table-column label="比例" sortable align="center" min-width="100">
            <template v-slot="{ row }">
              <span>{{ row.fbPer }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="INS">
          <el-table-column
            prop="ins"
            label="INS"
            sortable
            align="center"
            min-width="100"
          ></el-table-column>
          <el-table-column label="比例" sortable align="center" min-width="100">
            <template v-slot="{ row }">
              <span>{{ row.insPer }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="Snap">
          <el-table-column
            prop="snap"
            label="Snap"
            sortable
            align="center"
            min-width="100"
          ></el-table-column>
          <el-table-column label="比例" sortable align="center" min-width="100">
            <template v-slot="{ row }">
              <span>{{ row.snapPer }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="KW">
          <el-table-column
            prop="kw"
            label="KW"
            sortable
            align="center"
            min-width="100"
          ></el-table-column>
          <el-table-column label="比例" sortable align="center" min-width="100">
            <template v-slot="{ row }">
              <span>{{ row.kwPer }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import ChannelInfo from '@/components/Table/CustomColumn/ChannelColumn'
import { tableHeightMixin } from '@/mixin'
import { deliverStatsApi } from '@/api'
import { ipTypeOptions } from '@/utils/options'
export default {
  components: {
    ChannelInfo,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        date: new Date().toISOString().slice(0, 10),
        type: 2,
      },
      loading: false,
      tableData: {},
      expandedChildren: new Set(), // 存储展开的 children 行 ID
      ipTypeOptions,
    }
  },
  created() {},
  methods: {
    queryReset() {
      this.queryParams = {
        date: new Date().toISOString().slice(0, 10),
        type: 2,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await deliverStatsApi.getDeliverPageChannelDateSummary(params)
      this.tableData = res
      this.$xloading.hide()
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        const values = data.map((item) => Number(item[column.property]))
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => prev + curr, 0)
        } else {
          sums[index] = '--'
        }
      })
      return sums
    },
    handlePageCollectRecord(row) {
      this.$router.push({
        path: '/pagestats/pagecollectrecord',
        query: {
          date: row.date,
          type: row.type,
          areaId: row.areaId,
          cid: row.cId,
        },
      })
    },
  },
}
</script>
