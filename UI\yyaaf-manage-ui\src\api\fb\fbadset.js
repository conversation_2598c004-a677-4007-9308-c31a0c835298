import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/fBAdSet/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/fBAdSet/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addFBAdSet(data) {
  return request({
    url: '/fBAdSet/add',
    method: 'post',
    data: data,
  })
}

export function editFBAdSet(data) {
  return request({
    url: '/fBAdSet/edit',
    method: 'post',
    data: data,
  })
}

export function delFBAdSet(id) {
  return request({
    url: '/fBAdSet/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function changeStatus(data) {
  return request({
    url: '/fBAdSet/changeStatus',
    method: 'post',
    data: data,
  })
}

//----------FBAdSet结束----------
