<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="工单标题">
          <el-input v-model="queryParams.title"></el-input>
        </el-form-item>
        <el-form-item label="工单Url">
          <el-input v-model="queryParams.url"></el-input>
        </el-form-item>
        <el-form-item label="工单状态">
          <x-select
            show-default
            v-model="queryParams.state"
            url="/options/getCounterOrderConfigStateTypes"
          ></x-select>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['counterOrderConfig:add']"
          >新增</el-button
        >
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleBatchAdd"
          v-permission="['counterOrderConfig:add']"
          >批量新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="title" label="标题" align="center" min-width="200"></el-table-column>
        <el-table-column
          prop="platform"
          label="平台"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column prop="url" label="工单URL/UUID" align="center" min-width="700">
          <template v-slot="{ row }">
            <span>{{ row.url }}</span
            ><br />
            <span>{{ row.uuId }}</span>
          </template>
        </el-table-column>
        <el-table-column label="启用状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.enable">启用</el-tag>
            <el-tag type="danger" size="medium" v-else>禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="任务状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag size="medium" v-if="row.state == 0">等待抓取</el-tag>
            <el-tag type="success" size="medium" v-else-if="row.state == 1">抓取中</el-tag>
            <el-tag type="warning" size="medium" v-else-if="row.state == 9">抓取错误</el-tag>
            <el-tag type="danger" size="medium" v-else-if="row.state == 99">订单过期</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="lastStartSpiderTime"
          label="添加时间/过期时间"
          align="center"
          min-width="200"
        >
          <template v-slot="{ row }">
            <span>{{ row.createOn }}</span
            ><br />
            <span>{{ row.expireTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="lastStartSpiderTime"
          label="抓取时间/完成时间"
          align="center"
          min-width="200"
        >
          <template v-slot="{ row }">
            <span>{{ row.lastStartSpiderTime }}</span
            ><br />
            <span>{{ row.lastFinishSpiderTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="errorMsg"
          label="错误信息"
          align="center"
          min-width="200"
        ></el-table-column>

        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['counterOrderConfig:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['counterOrderConfig:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
    <batch-dialog ref="batchDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { counterOrderConfigApi } from '@/api'
import EditDialog from './edit'
import BatchDialog from './batch'
export default {
  components: {
    EditDialog,
    BatchDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        state: -1,
      },
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {
        state: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await counterOrderConfigApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleBatchAdd() {
      this.$refs.batchDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await counterOrderConfigApi.delCounterOrderConfig(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
