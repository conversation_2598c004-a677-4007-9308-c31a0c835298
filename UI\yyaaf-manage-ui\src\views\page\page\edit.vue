<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="id" prop="id">
            <el-input :disabled="true" v-model="form.id" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="统计地区" prop="areaId">
            <x-select
              v-model="form.areaId"
              url="/deliverArea/options"
              style="width: 100%"
            ></x-select>
            <div class="form-tip">
              <i class="el-icon-info"></i>
              统计地区必须与投放任务的地区相同，要不然会获取不到主号，请注意！
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板类别" prop="cateId">
            <x-select
              filterable
              v-model="form.cateId"
              url="/category/options"
              @change="handleCategoryChange(true)"
            ></x-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板" prop="tId">
            <x-select filterable v-model="form.tId" :options="templateOptions"></x-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="问候文字" prop="wsText">
            <el-input
              type="textarea"
              :autosize="{ minRows: 3 }"
              v-model="form.wsText"
              placeholder="请输入问候携带的文字"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="渠道标识" prop="cId">
            <x-select filterable v-model="form.cId" url="/deliverChannel/options"></x-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="像素" prop="pixelId">
            <el-input v-model="form.pixelId" placeholder="请输入像素Id" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用" prop="enable">
            <x-radio v-model="form.enable" button :options="enableOptions" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { enableOptions } from '@/utils/options'
import { pageApi, templateApi } from '@/api'
import { randomString } from '@/utils'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {
        title: randomString(32),
        enable: true,
      },
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        wsText: [{ required: true, message: '请输入问候文字', trigger: 'blur' }],
        areaId: [{ required: true, message: '请选择地区', trigger: 'blur' }],
        cateId: [{ required: true, message: '请选择类别', trigger: 'blur' }],
        tId: [{ required: true, message: '请选择模板', trigger: 'blur' }],
        cId: [{ required: true, message: '请输入渠道标识', trigger: 'blur' }],
      },
      templateOptions: [],
      enableOptions,
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await pageApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
        await this.handleCategoryChange()
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {
        title: randomString(32),
        enable: true,
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await pageApi.editPage(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await pageApi.addPage(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },

    async handleCategoryChange(reset = false) {
      const res = await templateApi.getTemplateOptions({ id: this.form.cateId })
      if (res.code == 0) {
        this.templateOptions = res.data
        if (reset) {
          this.form.tId = undefined
        }
      }
    },
  },
}
</script>

<style scoped>
.form-tip {
  margin-top: 5px;
  font-size: 13px;
  color: #ec9a4d;
  display: flex;
  align-items: center;
}

.form-tip i {
  margin-right: 4px;
}
</style>
