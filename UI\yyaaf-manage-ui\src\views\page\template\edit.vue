<template>
  <x-dialog
    v-loading="loading"
    :title="title"
    :visible.sync="visible"
    width="60%"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="抓取URL" prop="originalUrl">
            <el-input v-model="form.originalUrl" placeholder="请输入抓取链接" style="width: 80%" />
            <!-- 添加抓取按钮 -->
            <el-button @click="spiderPage" type="primary">抓取</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入模板名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类别" prop="cid">
            <x-select v-model="form.cId" url="/category/options" style="width: 100%"></x-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用" prop="enable">
            <x-radio v-model="form.enable" button :options="enableOptions" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="内容" prop="content">
            <el-card shadow="never" style="border-radius: 8px; padding: 10px">
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 10px;
                "
              >
                <span style="font-weight: bold; font-size: 14px">{{
                  showSourceCode ? '源码模式' : '编辑模式'
                }}</span>
                <el-button type="primary" size="small" @click="toggleSourceMode">
                  {{ showSourceCode ? '切换到编辑模式' : '切换到源码模式' }}
                </el-button>
              </div>

              <tinymce
                v-if="!showSourceCode"
                v-model="form.content"
                :upload-temp="uploadTemp"
                :uploadTempId="uploadTempId"
              />

              <textarea v-else v-model="form.content" class="source-code-textarea"></textarea>
            </el-card>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { enableOptions } from '@/utils/options'
import Tinymce from '@/components/Tinymce'
import { templateApi } from '@/api'
import { randomString } from '@/utils'

export default {
  components: {
    Tinymce,
  },
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {
        enable: true,
        tempId: randomString(32),
      },
      rules: {
        name: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
      },
      uploadTemp: 'preview',
      uploadTempId: undefined,
      enableOptions,
      showSourceCode: false, // 控制是否显示源码模式
    }
  },
  created() {
    this.uploadTempId = this.form.tempId
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await templateApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
        this.uploadTempId = res.data.tempId
        this.uploadTemp = 'release'
      }
      this.$xloading.hide()
    },

    close() {
      this.visible = false
    },

    reset() {
      this.form = {
        name: undefined,
        content: '',
        enable: true,
        tempId: randomString(32),
      }
      this.uploadTempId = this.form.tempId
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await templateApi.editTemplate(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await templateApi.addTemplate(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },

    async spiderPage() {
      this.$xloading.show()
      const data = Object.assign({}, this.form)
      const res = await templateApi.spiderTemplate(data)
      if (res && res.code == 0) {
        this.form.content = res.data.content
        this.uploadTemp = 'preview'
      } else {
        this.$xMsgError('抓取失败！' + res.msg)
      }
      this.$xloading.hide()
    },

    toggleSourceMode() {
      this.showSourceCode = !this.showSourceCode
    },
  },
}
</script>

<style scoped>
.source-code-textarea {
  width: 100%;
  height: 800px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  outline: none;
  resize: vertical;
  background: #f9f9f9;
}
.source-code-textarea:focus {
  border-color: #409eff;
  background: #fff;
}
</style>
