import request from '@/utils/request'

export function getTaskStatColumns() {
  return request({
    url: '/deliverStats/getTaskStatColumns',
    method: 'get',
  })
}

export function getTaskStatsList(params) {
  return request({
    url: '/deliverStats/getTaskStatsList',
    method: 'get',
    params,
  })
}

export function getActualTaskStatsList(params) {
  return request({
    url: '/deliverStats/getActualTaskStatsList',
    method: 'get',
    params,
  })
}

export function getChannelStatsList(params) {
  return request({
    url: '/deliverStats/getChannelStatsList',
    method: 'get',
    params,
  })
}

export function getAreaStatsList(params) {
  return request({
    url: '/deliverStats/getAreaStatsList',
    method: 'get',
    params,
  })
}

export function getDeliverPageTaskDateSummary(params) {
  return request({
    url: '/deliverStats/getDeliverPageTaskDateSummary',
    method: 'get',
    params,
  })
}

export function getDeliverPageChannelDateSummary(params) {
  return request({
    url: '/deliverStats/getDeliverPageChannelDateSummary',
    method: 'get',
    params,
  })
}
