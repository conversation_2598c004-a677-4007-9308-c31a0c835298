<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索
        <el-form ref="queryForm" label-width="80px" inline size="mini">
          <el-form-item label="Id">
            <el-input v-model="queryParams.name"></el-input>
          </el-form-item>
   
          <el-form-item>
            <el-button size="mini" type="primary" icon="el-icon-search"  @click="$refs.table.refresh(true)">搜索</el-button>
            <el-button size="mini" icon="el-icon-refresh"  @click="queryReset">重置</el-button>
          </el-form-item>
        </el-form>
  
        <el-row ref="toolbar" class="table-toolbar">
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd" v-permission="['fBUser:add']">新增</el-button>
        </el-row> -->

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="id" label="用户Id" align="center" min-width="150"></el-table-column>
        <el-table-column
          prop="name"
          label="用户昵称"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column prop="type" label="账户类型" align="center" min-width="150">
          <template v-slot="{ row }">
            <el-tag v-if="row.type == 'User'">用户账户</el-tag>
            <el-tag type="info" v-if="row.type == 'Public'">公共主页账户</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createOn"
          label="抓取时间"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="lastUpdateTime"
          label="最后更新时间"
          align="center"
          min-width="150"
        ></el-table-column>
        <!--<el-table-column prop="type" label="Cookie是否过期" align="center" min-width="150">
          <template v-slot="{ row }">
            <el-tag type="success" v-if="row.cookieExpires">正常</el-tag>
            <el-tag type="danger" v-if="!row.cookieExpires">过期</el-tag>
          </template>
        </el-table-column>-->
        <el-table-column label="授权状态" align="center" min-width="150">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.oAuthState == 1">已授权</el-tag>
            <el-tag type="danger" size="medium" v-else-if="row.oAuthState == 0">未授权</el-tag>
            <el-tag type="warning" size="medium" v-else-if="row.oAuthState == 9">授权过期</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="oAuthExpiresTime"
          label="授权过期时间"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleOAuth(scope.row)"
              v-permission="['fBUser:oauth']"
              >授权</el-button
            >
            <!--<el-button size="mini" 
                type="warning"
                @click="handleEdit(scope.row)"
                v-permission="['fBUser:edit']"
              >修改</el-button>
              <el-button size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
                v-permission="['fBUser:delete']"
              >删除</el-button>-->
          </template>
        </el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { fBUserApi } from '@/api'
export default {
  components: {},
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await fBUserApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await fBUserApi.delFBUser(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },

    async handleOAuth(row) {
      this.$xloading.show()
      const res = await fBUserApi.getOAthUrl(row.id)
      if (res.code == 0) window.open(res.data)
      else this.$xMsgError('删除失败！' + res.msg)
      this.$xloading.hide()
    },
  },
}
</script>
