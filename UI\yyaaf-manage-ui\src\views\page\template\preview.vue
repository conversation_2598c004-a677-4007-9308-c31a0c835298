<template>
  <div>
    <iframe :srcdoc="htmlContent" class="iframe-preview"></iframe>
  </div>
</template>

<script>
import { templateApi } from '@/api'
export default {
  data() {
    return {
      htmlContent: '',
    }
  },
  async created() {
    let query = this.$router.history.current.query
    if (query.id) await this.preview(query.id)
  },
  methods: {
    async preview(id) {
      this.$xloading.show()
      const res = await templateApi.preview(id)
      if (res.code == 0) {
        this.htmlContent = res.data.content
      }
      this.$xloading.hide()
    },
  },
}
</script>

<style scoped>
.iframe-preview {
  width: 100%;
  height: 800px;
  border: none;
}
</style>
