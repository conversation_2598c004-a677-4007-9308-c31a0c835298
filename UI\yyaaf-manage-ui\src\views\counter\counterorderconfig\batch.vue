
<template>
    <x-dialog
      width="1100px"
      :title="title"
      :visible.sync="visible"
      v-loading="loading"
      @submit="submitForm"
      @cancel="close">
  
      <el-form ref="form" :model="form" label-width="150px" size="large">
        <el-row>
            <el-col :span="24">
                <el-form-item label="批量添加工单配置" prop="counterOrderConfigString">
                    <el-input
                    type="textarea"
                    :autosize="{ minRows: 20 }"
                    v-model="form.counterOrderConfigString"
                    placeholder="请输入工单单配置批量添加字符串，格式：URL@Pwd@Title换行URL2@Pwd2@Title"
                    style="width: 900px;" 
                    />
                </el-form-item>
            </el-col>
        </el-row>
      </el-form>
    </x-dialog>
  </template>
  
  <script>
  import { enableOptions } from '@/utils/options'
  import { counterOrderConfigApi } from '@/api'
  export default {
    data () {
      return {
        title: '',
        visible: false,
        loading: false,
        form: {},
        enableOptions,
      }
    },
    methods:{
      add () {
        this.reset()
        this.title = '批量新增'
        this.visible = true
      },
      close() {
        this.reset()
        this.visible = false
      },
  
      reset () {
        this.form = {}
        this.$xResetForm('form')
      },
  
      submitForm () {
        this.$refs['form'].validate(async valid => {
          // console.log('submitForm valid: ', valid, 'data: ', this.form)
          if (valid) {
            this.$xloading.show()
            const data = Object.assign({}, this.form)
            const res = await counterOrderConfigApi.batchCounterOrderConfig(data)
              this.$xloading.hide()
              if (res.code == 0) {
                this.$xMsgSuccess('批量添加成功')
                this.close()
                this.$emit('ok')
              } else {
                this.$xMsgError('操作失败！' + res.msg)
              }
          }
        })
      }
    }
  }
  </script>
  
  <style>
  
  </style>
  