import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/deliverChannel/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/deliverChannel/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addDeliverChannel(data) {
  return request({
    url: '/deliverChannel/add',
    method: 'post',
    data: data,
  })
}

export function editDeliverChannel(data) {
  return request({
    url: '/deliverChannel/edit',
    method: 'post',
    data: data,
  })
}

export function delDeliverChannel(id) {
  return request({
    url: '/deliverChannel/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function getChannelOptions() {
  return request({
    url: '/deliverChannel/options',
    method: 'get',
  })
}

//----------DeliverChannel结束----------
