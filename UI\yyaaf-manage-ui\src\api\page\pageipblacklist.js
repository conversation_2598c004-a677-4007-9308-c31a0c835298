import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/pageIPBlackList/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/pageIPBlackList/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addPageIPBlackList(data) {
  return request({
    url: '/pageIPBlackList/add',
    method: 'post',
    data: data,
  })
}

export function editPageIPBlackList(data) {
  return request({
    url: '/pageIPBlackList/edit',
    method: 'post',
    data: data,
  })
}

export function delPageIPBlackList(id) {
  return request({
    url: '/pageIPBlackList/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------PageIPBlackList结束----------
