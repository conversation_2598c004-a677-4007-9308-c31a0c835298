<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="Host">
          <el-input v-model="queryParams.host"></el-input>
        </el-form-item>
        <el-form-item label="FBclid">
          <el-input v-model="queryParams.fBclid"></el-input>
        </el-form-item>
        <el-form-item label="IP">
          <el-input v-model="queryParams.ip"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="date" label="日期" align="center" min-width="100">
          <template v-slot="{ row }">
            <span>{{ row.date.slice(0, 10) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="host" label="域名" align="center" min-width="200">
          <template v-slot="{ row }">
            <span>{{ row.host }}</span>
            <br />
            <div>
              <el-button
                size="mini"
                type="warning"
                @click="handleAddToBlackList(row)"
                v-permission="['pagehostdate:addblack']"
                >添加</el-button
              >
              <el-button
                size="mini"
                type="danger"
                @click="handleRemoveFromBlackList(row)"
                v-permission="['pagehostdate:removeblack']"
                >移除</el-button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="fBclid" label="FBclid" align="center" min-width="300">
          <template v-slot="{ row }">
            <span style="font-size: 12px">{{ row.fBclid }}</span>
            <br />
            <div>
              <el-button
              size="mini"
              type="warning"
              @click="handleAddToIdentifierBlackList(scope.row)"
              v-permission="['pagehostdate:addblack']"
              >添加标识黑名单</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleRemoveFromIdentifierBlackList(scope.row)"
              v-permission="['pagehostdate:removeblack']"
              >移除标识黑名单</el-button
            >
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="ip" label="IP/国家/城市" align="center" min-width="280">
          <template v-slot="{ row }">
            <div class="ip-location-info">
              <!-- IP地址 -->
              <div class="ip-row">
                <i class="el-icon-connection" style="color: #409eff; margin-right: 5px"></i>
                <el-tag type="primary" size="small" effect="light">{{ row.ip }}</el-tag>
              </div>

              <!-- 国家信息 -->
              <div v-if="row.country" class="location-row">
                <i class="el-icon-location" style="color: #67c23a; margin-right: 5px"></i>
                <el-tag type="success" size="mini" effect="plain">{{ row.country }}</el-tag>
              </div>

              <!-- 城市信息 -->
              <div v-if="row.city" class="location-row">
                <i class="el-icon-map-location" style="color: #e6a23c; margin-right: 5px"></i>
                <el-tag type="warning" size="mini" effect="plain">{{ row.city }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="count"
          label="访问量"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column prop="status" label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.status == 0" type="success">正常</el-tag>
            <el-tag v-else-if="row.status == 1" type="danger">异常</el-tag>
            <el-tag v-else type="danger">黑名单</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="hostStatus" label="域名状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.hostStatus == 0" type="success">正常</el-tag>
            <el-tag v-else type="danger">黑名单</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="identifierStatus" label="标识状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-if="row.identifierStatus == 0" type="success">正常</el-tag>
            <el-tag v-else type="danger">黑名单</el-tag>
          </template>
        </el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { pageHostDateApi } from '@/api'
export default {
  components: {},
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        date: new Date().toISOString().slice(0, 10),
      },
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {
        date: new Date().toISOString().slice(0, 10),
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await pageHostDateApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    async handleAddToBlackList(row) {
      this.$confirm(`是否确认添加【${row.host}】到域名黑名单？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          row.blackType = 1
          const res = await pageHostDateApi.addToBlackList(row)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    async handleRemoveFromBlackList(row) {
      this.$confirm(`是否确认从域名黑名单中移除【${row.host}】？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          row.blackType = 1
          const res = await pageHostDateApi.removeBlackList(row)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    async handleAddToIdentifierBlackList(row) {
      this.$confirm(`是否确认添加【${row.fBclid}】到标识黑名单？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          row.blackType = 2
          const res = await pageHostDateApi.addToBlackList(row)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    async handleRemoveFromIdentifierBlackList(row) {
      this.$confirm(`是否确认从标识黑名单中移除【${row.fBclid}】？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          row.blackType = 2
          const res = await pageHostDateApi.removeBlackList(row)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
  },
}
</script>

<style scoped>
.ip-location-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px 4px;
}

.ip-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2px;
}

.location-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2px 0;
}

.status-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 8px 4px;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.status-tag {
  font-size: 11px !important;
  padding: 2px 6px !important;
  font-weight: 600;
  min-width: 35px;
  text-align: center;
}

.ip-location-info .el-tag {
  font-weight: 500;
  border-radius: 4px;
}

.ip-location-info .el-icon-connection {
  font-size: 14px;
}

.ip-location-info .el-icon-location,
.ip-location-info .el-icon-map-location {
  font-size: 12px;
}

.ip-location-info .el-icon-cpu,
.ip-location-info .el-icon-user {
  font-size: 12px;
}

/* 鼠标悬停效果 */
.ip-location-info:hover .el-tag {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* 状态标签特殊样式 */
.status-tag.el-tag--success.el-tag--dark {
  background-color: #67c23a;
  border-color: #67c23a;
  color: #fff;
}

.status-tag.el-tag--info.el-tag--dark {
  background-color: #909399;
  border-color: #909399;
  color: #fff;
}

/* 主号/像素列样式 */
.account-pixel-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px 4px;
}

.account-row,
.pixel-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2px 0;
}

/* 操作列样式 */
.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* Referrer 列样式 */
.referrer-column {
  display: flex;
  align-items: flex-start;
  width: 100%;
}

.referrer-content {
  width: 100%;
  display: flex;
  padding: 4px 0;
  flex-direction: column;
}

.referrer-url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #303133;
  word-break: break-all;
  word-wrap: break-word;
  display: flex;
  align-items: flex-start;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  max-height: 120px;
  overflow-y: auto;
}

.referrer-url i {
  flex-shrink: 0;
  margin-top: 1px;
}

.no-referrer-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.no-referrer-text {
  font-size: 12px;
  color: #c0c4cc;
  font-style: italic;
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .operation-buttons {
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .referrer-url {
    font-size: 11px;
    padding: 6px 8px;
    max-height: 100px;
  }

  .referrer-column {
    padding: 6px 8px;
  }

  .account-pixel-info {
    padding: 6px 2px;
    gap: 4px;
  }

  .account-pixel-info .el-tag {
    font-size: 10px;
    padding: 1px 4px;
  }
}
</style>
