<template>
    <div class="app-container">
      <el-card>
        <el-form ref="queryForm" label-width="80px" inline size="mini">
            <el-form-item label="账号名称">
                <el-input v-model="queryParams.account_name"></el-input>
            </el-form-item>
            <el-form-item label="广告名称">
                <el-input v-model="queryParams.ad_name"></el-input>
            </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.date"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-search"
              @click="$refs.table.refresh(true)"
              >搜索</el-button
            >
            <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
          </el-form-item>
        </el-form>
  
        <!-- 表格 -->
        <x-table
          ref="table"
          v-loading="loading"
          :data="tableData"
          row-key="id"
          :loadData="getList"
          :height="tableHeight"
        >
          <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
          <el-table-column prop="date" label="日期" align="center" width="150"></el-table-column>
          <el-table-column label="账号" align="center" min-width="200">
            <template v-slot="{ row }">
              <span>{{row.account_name}}</span>
              <br/>
              <el-tag type="default" size="medium">{{row.account_id}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="用户" align="center" min-width="200">
            <template v-slot="{ row }">
              <span>{{row.fbUserName}}</span>
              <br/>
              <el-tag type="default" size="medium">{{row.fbUserId}}</el-tag>
            </template>
          </el-table-column>
          <!--<el-table-column label="系列" align="center" min-width="200">
            <template v-slot="{ row }">
              <span>{{row.campaign_name}}</span>
              <br/>
              <el-tag type="default" size="medium">{{row.campaign_id}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="组" align="center" min-width="200">
            <template v-slot="{ row }">
              <span>{{row.adset_name}}</span>
              <br/>
              <el-tag type="default" size="medium">{{row.adset_id}}</el-tag>
            </template>
          </el-table-column>-->
          <el-table-column label="广告" align="center" min-width="200">
            <template v-slot="{ row }">
              <span>{{row.ad_name}}</span>
              <br/>
              <el-tag type="default" size="medium">{{row.ad_id}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="花费" align="center" min-width="150">
            <template v-slot="{ row }">
                <div class="coupon-price">{{row.spend.toFixed(2)}} {{row.account_currency}}</div>
            </template>
          </el-table-column>
          <el-table-column label="数据" align="left" min-width="150">
            <template v-slot="{ row }">
              <span class="price">CPM: {{row.cpm.toFixed(2)}}</span>
              <br/>
              <span class="price">CPC: {{row.cpc.toFixed(2)}}</span>
              <br/>
              <span class="price">CTR: {{row.ctr.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="展示数据" align="left" min-width="150">
            <template v-slot="{ row }">
              <span class="price">展示次数: {{row.impressions}}</span>
              <br/>
              <span class="price">覆盖人数: {{row.reach}}</span>
              <br/>
              <span class="price">频次: {{row.frequency.toFixed(2)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="点击数据" align="left" min-width="150">
            <template v-slot="{ row }">
              <span class="price">总点击: {{row.clicks}}</span>
              <br/>
              <span class="price">链接点击: {{row.inline_link_clicks}}</span>
            </template>
          </el-table-column>
          <el-table-column  label="播放率" align="left" min-width="150">
            <template v-slot="{ row }">
              <span class="price">播放25%: {{row.video_p25_watched_actions_value== null ? 0: row.video_p25_watched_actions_value}}</span>
              <br/>
              <span class="price">播放50%: {{row.video_p50_watched_actions_value== null ? 0: row.video_p50_watched_actions_value}}</span>
              <br/>
              <span class="price">播放75%: {{row.video_p75_watched_actions_value== null ? 0: row.video_p75_watched_actions_value}}</span>
              <br/>
              <span class="price">播放95%: {{row.video_p95_watched_actions_value == null ? 0: row.video_p95_watched_actions_value}}</span>
              <br/>
              <span class="price">播放100%: {{row.video_p100_watched_actions_value == null ? 0: row.video_p100_watched_actions_value}}</span>
            </template>
          </el-table-column>
        </x-table>
      </el-card>
    </div>
  </template>
  
  <script>
  import dayjs from 'dayjs'
  import { tableHeightMixin } from '@/mixin'
  import { fBadinsightsApi } from '@/api'
  export default {
    mixins: [tableHeightMixin],
    data() {
      return {
        queryParams: {
        },
        loading: false,
        tableData: {},
        channelOptions: [],
      }
    },
    created() {
        this.reset()
        let query = this.$router.history.current.query
        if(query.account_id)
            this.queryParams.account_id = query.account_id
    },
    methods: {
      queryReset() {
        this.queryParams = {
        }
        this.reset()
        this.$refs.table.refresh(true)
      },
      async getList(params) {
        this.$xloading.show()
        params = Object.assign({}, params, this.queryParams)
        if (this.queryParams.date) {
          params.beginTime = this.queryParams.date[0]
          params.endTime = this.queryParams.date[1]
        }
        const res = await fBadinsightsApi.getList(params)
        this.tableData = res
        this.$xloading.hide()
      },
      reset() {
        const beginDate = dayjs().format('YYYY-MM-DD')
        const endDate = dayjs().format('YYYY-MM-DD')
        this.queryParams.date = [beginDate, endDate]
      },
    },
  }
  </script>
  
  <style lang="scss" scoped>
  .price {
    text-align: left;
    font-size: 13px;
  }
  .coupon-price {
    font-size: 14px;
    color: #fd7f34;
  }
  .red {
    color: red;
  }
  </style>
  