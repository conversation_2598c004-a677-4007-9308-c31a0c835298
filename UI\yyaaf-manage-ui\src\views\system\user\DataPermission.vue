<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="visible = false"
    width="800px"
  >
    <el-form ref="form" :model="form" label-width="120px" size="medium">
      <el-form-item label="用户信息">
        <el-tag type="primary" size="medium">{{ userInfo }}</el-tag>
      </el-form-item>

      <el-divider content-position="left">数据权限配置</el-divider>

      <!-- 任务权限 -->
      <el-form-item label="任务权限">
        <div class="permission-section">
          <div class="section-header">
            <el-checkbox
              v-model="taskAllSelected"
              @change="handleTaskAllChange"
              :indeterminate="taskIndeterminate"
            >
              全选任务
            </el-checkbox>
            <span class="selected-count">(已选择 {{ selectedTasks.length }} 项)</span>
          </div>
          <div class="options-container">
            <el-checkbox-group v-model="selectedTasks" @change="handleTaskChange">
              <el-checkbox
                v-for="task in taskOptions"
                :key="task.value"
                :label="task.value"
                class="option-item"
              >
                {{ task.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </el-form-item>

      <!-- 计数器权限 -->
      <el-form-item label="计数器权限">
        <div class="permission-section">
          <div class="section-header">
            <el-checkbox
              v-model="counterAllSelected"
              @change="handleCounterAllChange"
              :indeterminate="counterIndeterminate"
            >
              全选计数器
            </el-checkbox>
            <span class="selected-count">(已选择 {{ selectedCounters.length }} 项)</span>
          </div>
          <div class="options-container">
            <el-checkbox-group v-model="selectedCounters" @change="handleCounterChange">
              <el-checkbox
                v-for="counter in counterOptions"
                :key="counter.value"
                :label="counter.value"
                class="option-item"
              >
                {{ counter.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </el-form-item>
    </el-form>
  </x-dialog>
</template>

<script>
import { userdatapermissionApi } from '@/api'

export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {
        userId: null,
        permissions: [],
      },
      currentUser: null,

      // 任务相关
      taskOptions: [],
      selectedTasks: [],
      taskAllSelected: false,
      taskIndeterminate: false,

      // 计数器相关
      counterOptions: [],
      selectedCounters: [],
      counterAllSelected: false,
      counterIndeterminate: false,
    }
  },

  computed: {
    userInfo() {
      if (!this.currentUser) return ''
      return `${this.currentUser.nickName} (${this.currentUser.userName})`
    },
  },

  methods: {
    async open(user) {
      this.currentUser = user
      this.form.userId = user.id
      this.title = `数据权限配置 - ${user.nickName}`
      this.visible = true

      // 加载选项数据和用户当前权限
      await this.loadOptions()
      await this.loadUserPermissions()
    },

    async loadOptions() {
      try {
        this.loading = true

        // 加载任务选项
        const taskRes = await userdatapermissionApi.getTaskConfigOptions()
        if (taskRes.code === 0) {
          this.taskOptions = taskRes.data || []
        }

        // 加载计数器选项
        const counterRes = await userdatapermissionApi.getCounterOrderConfigOptions()
        if (counterRes.code === 0) {
          this.counterOptions = counterRes.data || []
        }
      } catch (error) {
        this.$xMsgError('加载选项数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    async loadUserPermissions() {
      try {
        this.loading = true

        const res = await userdatapermissionApi.getUserDataPermissions(this.form.userId)
        if (res.code === 0) {
          const permissions = res.data.permissions || []

          // 分离任务和计数器权限
          this.selectedTasks = permissions
            .filter((p) => p.resourceType === 'DeliverTaskConfig')
            .map((p) => p.resourceId)

          this.selectedCounters = permissions
            .filter((p) => p.resourceType === 'CounterOrderConfig')
            .map((p) => p.resourceId)

          // 更新全选状态
          this.updateTaskSelectState()
          this.updateCounterSelectState()
        }
      } catch (error) {
        this.$xMsgError('加载用户权限失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 任务权限处理
    handleTaskAllChange(val) {
      this.selectedTasks = val ? this.taskOptions.map((item) => item.value) : []
      this.taskIndeterminate = false
    },

    handleTaskChange() {
      this.updateTaskSelectState()
    },

    updateTaskSelectState() {
      const checkedCount = this.selectedTasks.length
      this.taskAllSelected = checkedCount === this.taskOptions.length
      this.taskIndeterminate = checkedCount > 0 && checkedCount < this.taskOptions.length
    },

    // 计数器权限处理
    handleCounterAllChange(val) {
      this.selectedCounters = val ? this.counterOptions.map((item) => item.value) : []
      this.counterIndeterminate = false
    },

    handleCounterChange() {
      this.updateCounterSelectState()
    },

    updateCounterSelectState() {
      const checkedCount = this.selectedCounters.length
      this.counterAllSelected = checkedCount === this.counterOptions.length
      this.counterIndeterminate = checkedCount > 0 && checkedCount < this.counterOptions.length
    },

    async submitForm() {
      try {
        this.loading = true

        // 构建权限数据
        const permissions = [
          ...this.selectedTasks.map((taskId) => ({
            resourceType: 'DeliverTaskConfig',
            resourceId: taskId,
          })),
          ...this.selectedCounters.map((counterId) => ({
            resourceType: 'CounterOrderConfig',
            resourceId: counterId,
          })),
        ]

        const data = {
          userId: this.form.userId,
          permissions: permissions,
        }

        const res = await userdatapermissionApi.saveUserDataPermissions(data)

        if (res.code === 0) {
          this.$xMsgSuccess('数据权限配置成功')
          this.visible = false
          this.$emit('ok')
        } else {
          this.$xMsgError('配置失败：' + res.msg)
        }
      } catch (error) {
        this.$xMsgError('配置失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    reset() {
      this.form = {
        userId: null,
        permissions: [],
      }
      this.currentUser = null
      this.selectedTasks = []
      this.selectedCounters = []
      this.taskAllSelected = false
      this.taskIndeterminate = false
      this.counterAllSelected = false
      this.counterIndeterminate = false
    },
  },
}
</script>

<style scoped>
.permission-section {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.selected-count {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.options-container {
  max-height: 200px;
  overflow-y: auto;
}

.option-item {
  display: block;
  margin-bottom: 8px;
  margin-right: 0;
  width: 100%;
}

.option-item:last-child {
  margin-bottom: 0;
}

/* 滚动条样式 */
.options-container::-webkit-scrollbar {
  width: 6px;
}

.options-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.options-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.options-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
