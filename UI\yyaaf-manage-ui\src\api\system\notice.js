import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/systemNotice/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/systemNotice/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addSystemNotice(data) {
  return request({
    url: '/systemNotice/add',
    method: 'post',
    data: data,
  })
}

export function editSystemNotice(data) {
  return request({
    url: '/systemNotice/edit',
    method: 'post',
    data: data,
  })
}

export function delSystemNotice(id) {
  return request({
    url: '/systemNotice/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function getNoticeInfo() {
  return request({
    url: '/systemNotice/getNoticeInfo',
    method: 'get',
    params: {},
  })
}

export function readNotice(id, type) {
  return request({
    url: '/systemNotice/readNotice',
    method: 'post',
    data: {
      id,
      type,
    },
  })
}

export function readAll() {
  return request({
    url: '/systemNotice/readAll',
    method: 'post',
    data: {},
  })
}

//----------SystemNotice结束----------
