(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-75fd001a"],{2:function(e,t){},"691a":function(e,t,n){},"7d92":function(e,t,n){"use strict";n.d(t,"c",(function(){return u})),n.d(t,"b",(function(){return p})),n.d(t,"a",(function(){return g}));n("f576"),n("ed50"),n("6b54");var r=n("3452"),o=n.n(r),i=(n("c198"),n("720d")),s=n.n(i);o.a.mode.CBC,o.a.pad.Pkcs7;var a="MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMfYk/CK1aLSxfIsQ94dXXsgbXVRLlva\nCdQl3egrNJ1+s0WJqgyPdFDIFw/TiwW6cVtYKN5Tm12rC1aVUhE7Mc0CAwEAAQ==",l="MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALkumIqQMhoRbM5opek++pTvyB/VZCtH\nJJqXj0olueiarjwMqFiVgyQjwREddT1y5Sijj1PH9GPLs4GLv9y/y50CAwEAAQ==",c="MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAuS6YipAyGhFszmil\n6T76lO/IH9VkK0ckmpePSiW56JquPAyoWJWDJCPBER11PXLlKKOPU8f0Y8uzgYu/\n3L/LnQIDAQABAkBZhV8UzTSLSZUyC4D5SwrUaT5ztTMhgNj/KvmIPMis2xc+1KRS\nKlSSx00+qdm4bKDSB0D80MI5r2FDyov7YUe5AiEA5gT9d+U4bBSguBara4sg62UG\nhp21XPsAdWVhpHg0+rsCIQDOGSF9asLMhNreIwgy88zVp/IkyBf/MGmsZYj82Y0J\nhwIgBOnmYEFNS0HFjSku0EVQlra5xPZpgWr7P4bC5ziKKTECIQDL900QjQbqVxUw\nQGVN38A5NrPKuQgesm/ygK345ujQowIgPNvJ7AO+SS+MPHdnVn9pk7Jwcmlo0aQb\nz85W+KC0dBE=";function u(e){return d(e,a)}function d(e,t){var n=new s.a;return n.setPublicKey(t),n.encrypt(e)}function m(e,t){var n=new s.a;return n.setPrivateKey(t),n.decrypt(e)}function p(e){return d(e,l)}function g(e){return m(e,c)}},"9ed6":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"login"},[n("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules}},[n("h3",{staticClass:"title"},[e._v(e._s(this.title)+"登录")]),e._v(" "),n("el-form-item",{attrs:{prop:"username"}},[n("el-input",{attrs:{type:"text","auto-complete":"off",placeholder:"账号"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}},[n("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"user"},slot:"prefix"})],1)],1),e._v(" "),n("el-form-item",{attrs:{prop:"password"}},[n("el-input",{attrs:{type:"password","auto-complete":"off",placeholder:"密码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}},[n("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"password"},slot:"prefix"})],1)],1),e._v(" "),n("el-checkbox",{staticStyle:{margin:"0px 0px 0px 0px"},model:{value:e.loginForm.rememberMe,callback:function(t){e.$set(e.loginForm,"rememberMe",t)},expression:"loginForm.rememberMe"}},[e._v("记住我")]),e._v(" "),n("el-form-item",{staticClass:"login-btn",staticStyle:{width:"100%","margin-bottom":"10px"}},[n("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,size:"medium",type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e.loading?n("span",[e._v("登 录 中...")]):n("span",[e._v("登 录")])])],1),e._v(" "),e.isAdminUser?n("el-form-item",[n("el-button",{staticStyle:{width:"100%"},attrs:{size:"medium"},nativeOn:{click:function(t){return t.preventDefault(),e.handleOAuth(t)}}},[n("span",[e._v("用户中心登录")])])],1):e._e()],1),e._v(" "),e._m(0)],1)},o=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"el-login-footer"},[n("span")])}],i=(n("96cf"),n("1da1")),s=(n("aef6"),n("83d6")),a=n.n(s),l=(n("a78e"),n("7d92")),c=n("5f87"),u=n("365c"),d={name:"Login",data:function(){var e=function(e,t,n){0==t.length?n(new Error("用户名不能为空")):n()},t=function(e,t,n){0==t.length?n(new Error("密码不能为空")):n()};return{title:a.a.title,loginForm:{username:"",password:""},loginRules:{username:[{required:!0,trigger:"blur",validator:e}],password:[{required:!0,trigger:"blur",validator:t}]},loading:!1,passwordType:"password",redirect:void 0,isAdminUser:!1}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},created:function(){this.checkIframe(),this.getUserLoginParams(),window.location.hostname.endsWith("dodourl.com")&&(this.isAdminUser=!0)},methods:{checkIframe:function(){if(window.self!==window.top){var e={url:window.location.origin};window.parent.postMessage({type:"OpenTab",data:e},"*")}},getUserLoginParams:function(){var e=Object(c["d"])(),t=Object(c["a"])(),n=Object(c["b"])();this.loginForm={username:null===e?this.loginForm.username:e,password:null===t?this.loginForm.password:Object(l["a"])(t),rememberMe:null!==n&&Boolean(n)}},showPwd:function(){var e=this;"password"===this.passwordType?this.passwordType="":this.passwordType="password",this.$nextTick((function(){e.$refs.password.focus()}))},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(t){if(!t)return!1;e.$xloading.show();var n=Object.assign({},e.loginForm);e.loginForm.rememberMe?(Object(c["k"])(e.loginForm.username),Object(c["h"])(Object(l["b"])(e.loginForm.password)),Object(c["i"])(e.loginForm.rememberMe)):Object(c["g"])(),n.password=Object(l["c"])(n.password),e.$store.dispatch("Login",n).then((function(){e.$router.push({path:e.redirect||"/"}),e.$xloading.hide(),window.location.reload()})).catch((function(t){e.$xloading.hide(),e.$xMsgError(t)}))}))},handleOAuth:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,n,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.$loading(),e.next=3,u["a"].getYYAuthUrl();case 3:n=e.sent,t.close(),0==n.code?(r=n.data,window.location.href=r):this.$xMsgError(n.msg);case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},m=d,p=(n("b4b1"),n("2877")),g=Object(p["a"])(m,r,o,!1,null,null,null);t["default"]=g.exports},b4b1:function(e,t,n){"use strict";n("691a")}}]);