<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="类别">
          <x-select
            filterable
            show-default
            v-model="queryParams.cateId"
            url="/category/options"
          ></x-select>
        </el-form-item>
        <el-form-item label="渠道">
          <x-select filterable v-model="queryParams.cId" url="/deliverChannel/options"></x-select>
        </el-form-item>

        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['page:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
        :cell-style="{ fontSize: '13px' }"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="title" label="页面标题" align="center" min-width="250">
          <template v-slot="{ row }">
            <span>{{ row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column label="页面标识/模板" align="center" min-width="280">
          <template v-slot="{ row }">
            <el-link type="primary" :underline="false" @click="handleDeliverUrl(row)">
              <span>{{ row.id }}</span>
            </el-link>
            <br />
            <el-link type="warning" :underline="false" @click="handleTemplatePreview(row)">
              <div>{{ row.tName }}</div>
            </el-link>
            <!--<el-link type="primary" :underline="false" @click="handleDeliverUrl(scope.row)">
              <span>{{ row.title }}</span>
            </el-link>-->
          </template>
        </el-table-column>
        <el-table-column prop="cateName" label="类别/地区" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ row.cateName }}</span>
            <br />
            <span>{{ row.areaName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="渠道" align="center" min-width="200">
          <template v-slot="{ row }">
            <channel-info :cId="row.cId" :cName="row.cName"></channel-info>
          </template>
        </el-table-column>
        <el-table-column
          prop="pixelId"
          label="像素"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column prop="createOn" label="时间" align="center" min-width="160">
          <template v-slot="{ row }">
            <span>{{ row.createOn }}</span>
            <br />
            <span>{{ row.editTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.enable">启用</el-tag>
            <el-tag type="danger" size="medium" v-else>禁用</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" min-width="230" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleDeliverUrl(scope.row)"
              v-permission="['page:preview']"
              >链接</el-button
            >
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['page:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['page:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import ChannelInfo from '@/components/Table/CustomColumn/ChannelColumn'
import { tableHeightMixin } from '@/mixin'
import { pageApi, templateApi } from '@/api'
import EditDialog from './edit'
export default {
  components: {
    ChannelInfo,
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await pageApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    handlePreview(row) {
      //this.$router.push({ path: '/page/preview', query: { id: row.id } })
      //window.open(`http://localhost:52833/api/page/PreviewPage?id=${row.id}`)
      window.open(`https://customer.729183.xyz/api/page/PreviewPage?id=${row.id}`)
    },
    async handleTemplatePreview(row) {
      this.$xloading.show()
      const res = await templateApi.preview(row.tId)
      const htmlContent = res
      const win = window.open()
      win.document.open()
      win.document.write(htmlContent)
      win.document.close()
      this.$xloading.hide()
    },
    async handleDeliverUrl(row) {
      this.$xloading.show()
      const res = await pageApi.deliverUrl(row.id)
      if (res.code == 0) {
        console.log(res.data)
        let url = res.data
        window.open(url)
      }
      this.$xloading.hide()
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await pageApi.delPage(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
