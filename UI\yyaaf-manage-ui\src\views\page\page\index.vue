<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="类别">
          <x-select
            filterable
            show-default
            v-model="queryParams.cateId"
            url="/category/options"
          ></x-select>
        </el-form-item>
        <el-form-item label="渠道">
          <x-select filterable v-model="queryParams.cId" url="/deliverChannel/options"></x-select>
        </el-form-item>

        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['page:add']"
          >新增</el-button
        >
        <el-button
          size="mini"
          type="success"
          icon="el-icon-upload2"
          @click="handleBatchEnable"
          v-permission="['page:edit']"
          >批量启用</el-button
        >
        <el-button
          size="mini"
          type="danger"
          icon="el-icon-download"
          @click="handleBatchDisable"
          v-permission="['page:edit']"
          >批量禁用</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
        :cell-style="{ fontSize: '13px' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column prop="title" label="页面标题" align="center" min-width="250">
          <template v-slot="{ row }">
            <span>{{ row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column label="页面标识/模板" align="center" min-width="280">
          <template v-slot="{ row }">
            <el-link type="primary" :underline="false" @click="handlePreviewDeliverUrl(row)">
              <span>{{ row.id }}</span>
            </el-link>
            <br />
            <el-link type="warning" :underline="false" @click="handleTemplatePreview(row)">
              <div>{{ row.tName }}</div>
            </el-link>
            <!--<el-link type="primary" :underline="false" @click="handlePreviewDeliverUrl(scope.row)">
              <span>{{ row.title }}</span>
            </el-link>-->
          </template>
        </el-table-column>
        <el-table-column prop="cateName" label="类别/地区" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>{{ row.cateName }}</span>
            <br />
            <span>{{ row.areaName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="渠道" align="center" min-width="200">
          <template v-slot="{ row }">
            <channel-info :cId="row.cId" :cName="row.cName"></channel-info>
          </template>
        </el-table-column>
        <el-table-column
          prop="pixelId"
          label="像素"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column prop="createOn" label="时间" align="center" min-width="160">
          <template v-slot="{ row }">
            <span>{{ row.createOn }}</span>
            <br />
            <span>{{ row.editTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.enable">启用</el-tag>
            <el-tag type="danger" size="medium" v-else>禁用</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" min-width="230" align="center">
          <template slot-scope="scope">
            <div>
              <el-button
                size="mini"
                type="primary"
                @click="handleCopyDeliverUrl(scope.row)"
                v-permission="['page:preview']"
                >复制链接</el-button
              >
              <el-button
                size="mini"
                type="primary"
                @click="handlePreviewDeliverUrl(scope.row)"
                v-permission="['page:preview']"
                >预览页面</el-button
              >
            </div>
            <div style="margin-top: 5px">
              <el-button
                size="mini"
                type="warning"
                @click="handleEdit(scope.row)"
                v-permission="['page:edit']"
                >修改页面</el-button
              >
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
                v-permission="['page:delete']"
                >删除页面</el-button
              >
            </div>
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import ChannelInfo from '@/components/Table/CustomColumn/ChannelColumn'
import { tableHeightMixin } from '@/mixin'
import { pageApi, templateApi } from '@/api'
import EditDialog from './edit'
export default {
  components: {
    ChannelInfo,
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
      // 选中的完整行数据
      selectedRows: [],
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await pageApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    handlePreview(row) {
      //this.$router.push({ path: '/page/preview', query: { id: row.id } })
      //window.open(`http://localhost:52833/api/page/PreviewPage?id=${row.id}`)
      window.open(`https://customer.729183.xyz/api/page/PreviewPage?id=${row.id}`)
    },
    async handleTemplatePreview(row) {
      this.$xloading.show()
      const res = await templateApi.preview(row.tId)
      const htmlContent = res
      const win = window.open()
      win.document.open()
      win.document.write(htmlContent)
      win.document.close()
      this.$xloading.hide()
    },
    async handlePreviewDeliverUrl(row) {
      this.$xloading.show()
      const res = await pageApi.previewDeliverUrl(row.id)
      if (res.code == 0) {
        this.$alert('请注意，预览链接5分钟会过期！', {
          confirmButtonText: '确定',
          type: 'info',
          callback: () => {
            console.log(res.data)
            let url = res.data
            window.open(url)
          },
        })
      }
      this.$xloading.hide()
    },
    async handleCopyDeliverUrl(row) {
      this.$xloading.show()
      const res = await pageApi.deliverUrl(row.id)
      if (res.code == 0) {
        console.log(res.data)
        let url = res.data
        this.copyToClipboard(url)
      }
      this.$xloading.hide()
    },
    // 复制链接到剪贴板
    async copyToClipboard(text) {
      try {
        // 优先使用现代 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(text)
          this.$xMsgSuccess('链接复制成功！')
        } else {
          // 降级到传统方法
          this.fallbackCopyToClipboard(text)
        }
      } catch (err) {
        console.error('复制失败:', err)
        // 如果现代API失败，尝试降级方法
        this.fallbackCopyToClipboard(text)
      }
    },

    // 降级复制方法（兼容旧浏览器）
    fallbackCopyToClipboard(text) {
      const textarea = document.createElement('textarea')
      textarea.value = text
      textarea.style.position = 'fixed'
      textarea.style.left = '-999999px'
      textarea.style.top = '-999999px'
      document.body.appendChild(textarea)
      textarea.focus()
      textarea.select()

      try {
        document.execCommand('copy')
        this.$xMsgSuccess('链接复制成功！')
      } catch (err) {
        console.error('复制失败:', err)
        this.$xMsgError('复制失败，请手动复制')
        // 显示链接供用户手动复制
        this.$alert(`请手动复制以下链接：\n${text}`, '复制链接', {
          confirmButtonText: '确定',
          type: 'info',
        })
      } finally {
        document.body.removeChild(textarea)
      }
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await pageApi.delPage(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    // 批量启用
    async handleBatchEnable() {
      if (this.selectedRows.length === 0) {
        this.$xMsgWarning('请选择要操作的页面！')
        return
      }

      this.$confirm(
        `是否确认对选中的 ${this.selectedRows.length} 个页面执行批量启用操作？`,
        '批量启用确认',
        {
          type: 'warning',
          confirmButtonText: '确认执行',
          cancelButtonText: '取消',
        }
      )
        .then(async () => {
          this.$xloading.show()

          let successCount = 0
          let failCount = 0
          const failedPages = []

          try {
            // 循环调用单个接口
            for (const row of this.selectedRows) {
              try {
                const res = await pageApi.changeEnable(row.id, true)
                if (res.code === 0) {
                  successCount++
                } else {
                  failCount++
                  failedPages.push(`${row.id}(${res.msg})`)
                }
              } catch (error) {
                failCount++
                failedPages.push(`${row.id}(${error.message})`)
              }
            }

            this.$xloading.hide()

            // 显示操作结果
            if (failCount === 0) {
              this.$xMsgSuccess(`批量启用操作成功，共处理 ${successCount} 个主号`)
            } else if (successCount === 0) {
              this.$xMsgError(`批量启用操作失败，${failCount} 个主号操作失败`)
            } else {
              this.$xMsgWarning(
                `批量启用操作完成，成功 ${successCount} 个，失败 ${failCount} 个。失败详情：${failedPages.join(
                  '、'
                )}`
              )
            }

            this.$refs.table.refresh()
          } catch (error) {
            this.$xloading.hide()
            this.$xMsgError('批量启用操作失败：' + error.message)
          }
        })
        .catch(() => {
          // 用户取消操作
        })
    },
    // 批量禁用
    async handleBatchDisable() {
      if (this.selectedRows.length === 0) {
        this.$xMsgWarning('请选择要操作的页面！')
        return
      }

      this.$confirm(
        `是否确认对选中的 ${this.selectedRows.length} 个页面执行批量禁用操作？`,
        '批量禁用确认',
        {
          type: 'warning',
          confirmButtonText: '确认执行',
          cancelButtonText: '取消',
        }
      )
        .then(async () => {
          this.$xloading.show()

          let successCount = 0
          let failCount = 0
          const failedPages = []

          try {
            // 循环调用单个接口
            for (const row of this.selectedRows) {
              try {
                const res = await pageApi.changeEnable(row.id, false)
                if (res.code === 0) {
                  successCount++
                } else {
                  failCount++
                  failedPages.push(`${row.id}(${res.msg})`)
                }
              } catch (error) {
                failCount++
                failedPages.push(`${row.id}(${error.message})`)
              }
            }

            this.$xloading.hide()

            // 显示操作结果
            if (failCount === 0) {
              this.$xMsgSuccess(`批量禁用操作成功，共处理 ${successCount} 个主号`)
            } else if (successCount === 0) {
              this.$xMsgError(`批量禁用操作失败，${failCount} 个主号操作失败`)
            } else {
              this.$xMsgWarning(
                `批量禁用操作完成，成功 ${successCount} 个，失败 ${failCount} 个。失败详情：${failedPages.join(
                  '、'
                )}`
              )
            }

            this.$refs.table.refresh()
          } catch (error) {
            this.$xloading.hide()
            this.$xMsgError('批量禁用操作失败：' + error.message)
          }
        })
        .catch(() => {
          // 用户取消操作
        })
    },
    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
