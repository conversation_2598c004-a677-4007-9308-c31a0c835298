// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.small-width {
  .el-button--mini {
    padding: 5px;
    width: 30px;
  }
}

.el-table .fixed-width .el-button--mini {
	// color: #409EFF;
	padding-left: 0;
	padding-right: 0;
	width: 60px;
}

.el-table .icon-btn .el-button--mini {
  padding-left: 7px;
  padding-right: 7px;
  margin-left: 7px;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// switch 激活的颜色
.el-switch.is-checked .el-switch__core {
  border-color: #13ce66;
  background-color: #13ce66;
}

